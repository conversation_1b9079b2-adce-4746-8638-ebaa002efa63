#!/bin/bash

# Usage: ./deploy.sh [environment] 
# Example: ./deploy.sh dev → Creates tag dev-0.1.11 (after dev-0.1.10)

# --- Validate Input ---
if [ -z "$1" ]; then
  echo "Error: Please specify an environment (e.g., dev, staging, prod)"
  exit 1
fi

ENV=$1  # e.g., "dev"

# --- Fetch Latest Tags ---
git fetch --tags

# --- Get Latest Tag for the Environment (Sorted by Version) ---
LATEST_TAG=$(git tag --list "$ENV-*" --sort=-v:refname | head -n 1)

if [ -z "$LATEST_TAG" ]; then
  # No tag exists for this env → start with 0.1.0
  NEW_TAG="$ENV-0.1.0"
  echo "No existing tag for '$ENV'. Starting with: $NEW_TAG"
else
  # Extract version (e.g., dev-0.1.10 → 0.1.10)
  VERSION=${LATEST_TAG#$ENV-}

  # Split into parts (0, 1, 10)
  IFS='.' read -r major minor patch <<< "$VERSION"

  # Increment patch (0.1.10 → 0.1.11)
  NEW_PATCH=$((patch + 1))
  NEW_TAG="$ENV-$major.$minor.$NEW_PATCH"
  echo "Incremented tag: $LATEST_TAG → $NEW_TAG"
fi

# --- Validate Git Status (Optional) ---
if ! git diff-index --quiet HEAD --; then
  read -p "Warning: Uncommitted changes exist. Continue? (y/n) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
  fi
fi

# --- Create and Push the New Tag ---
git tag -a "$NEW_TAG" -m "Release $NEW_TAG ($ENV environment)"
git push origin "$NEW_TAG"

echo "Successfully created and pushed: $NEW_TAG"