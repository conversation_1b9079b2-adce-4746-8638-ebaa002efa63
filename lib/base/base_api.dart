import 'dart:convert';
import 'dart:developer';
// import 'dart:io';

import 'package:pdl_superapp/utils/logger_service.dart';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/request/request.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/network_manager.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BaseApi extends GetConnect {
  // late SharedPreferences prefs;
  @override
  void onInit() async {
    httpClient.baseUrl = baseUrl;
    httpClient.timeout = const Duration(seconds: 15);
    // prefs = await SharedPreferences.getInstance();

    setupRequestModifier();
  }

  setupRequestModifier({bool? isFile}) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    // Additional headers

    httpClient.addRequestModifier((Request request) {
      String token = prefs.getString(kStorageToken) ?? '';
      String deviceId = prefs.getString(kDeviceId) ?? '';
      //  prefs.getString(kStorageToken) ?? '';
      token != '' ? request.headers['Authorization'] = 'Bearer $token' : null;
      if (!kIsWeb) {
        // if (isFile == true) {
        //   request.headers['Content-Type'] = 'multipart/form-data';
        // } else {

        // }
        request.headers['Content-Type'] = 'application/json';
        request.headers['Accept'] = 'application/json';
      }

      request.headers['device_id'] = deviceId;
      _print(doPrint: false, data: token);
      _print(doPrint: false, data: request.headers);
      _print(doPrint: false, data: request.files);
      return request;
    });
  }

  /// Check internet connection
  Future<bool> _checkInternetConnection() async {
    // If platform is web, always return true (assume connected)
    if (kIsWeb) {
      return true;
    }

    try {
      final networkManager = Get.find<NetworkManager>();

      // Jika sudah offline, langsung return false
      if (!networkManager.isOnline) return false;

      // Cek kualitas koneksi hanya jika online
      return await networkManager.checkConnectionQuality();
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error finding NetworkManager: $e');
      } catch (_) {
        log('Error finding NetworkManager: $e'); // Fallback to direct log
      }
      // Fallback ke pengecekan basic jika NetworkManager tidak tersedia
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    }
  }

  /// BASE FUNCTIONS
  /// Fetch data from API with offline cache support
  Future<void> apiFetch({
    required String url,
    required BaseControllers controller,
    int code = 0,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      // Check internet connection first
      final hasConnection = await _checkInternetConnection();

      // Inisialisasi FirestoreServices terlebih dahulu, baik online maupun offline
      FirestoreServices firestoreServices;
      try {
        firestoreServices = Get.find<FirestoreServices>();
        Get.find<LoggerService>().log("FirestoreServices ditemukan");
      } catch (e) {
        Get.find<LoggerService>().log(
          "FirestoreServices tidak ditemukan, membuat instance baru",
        );
        firestoreServices = Get.put(FirestoreServices());
      }

      if (!hasConnection) {
        // No internet connection
        Get.find<LoggerService>().log("Tidak ada koneksi internet");

        // Try to get cached data from Firestore for all URLs (including public)
        Get.find<LoggerService>().log(
          "Mencoba mengambil data dari cache untuk URL: $url",
        );

        final cachedData = await firestoreServices.getApiCallData(url: url);

        if (cachedData != null) {
          // Use cached data if available
          Get.find<LoggerService>().log("Berhasil menggunakan data dari cache");
          Get.find<LoggerService>().log(
            "Tipe data cache: ${cachedData.runtimeType}",
          );

          return controller.loadSuccess(
            requestCode: code,
            response: cachedData,
            statusCode: 200, // Assume success status code
          );
        } else {
          // No cached data available
          Get.find<LoggerService>().log(
            "Tidak ada data cache yang tersedia untuk URL: $url",
          );
          return controller.loadError(
            "Tidak ada koneksi internet dan tidak ada data cache yang tersedia",
          );
        }
      }

      _print(doPrint: debug, data: url);

      final response = await get(url);

      _print(doPrint: debug, data: response.body ?? {});
      if (response.status.hasError) {
        if (response.statusCode == 401) {
          Utils.setLoggedOut();
        }
        // Get.snackbar(
        //   'Failed',
        //   '${response.body['error_description'] ?? '-'}',
        //   snackPosition: SnackPosition.BOTTOM,
        //   backgroundColor: kColorErrorContainer,
        //   colorText: kColorErrorText,
        // );
        return controller.loadFailed(requestCode: code, response: response);
      }

      // Store successful API response in Firestore for offline access
      // Simpan semua respons API, termasuk URL publik
      Get.find<LoggerService>().log(
        "Menyimpan respons API ke Firestore untuk akses offline: $url",
      );
      await _storeApiCallData(url: url, responseData: response.body ?? {});
      Get.find<LoggerService>().log(
        "Data berhasil disimpan ke Firestore untuk URL: $url",
      );

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log(e.toString());

      // Jika terjadi error (misalnya timeout), coba ambil dari cache
      try {
        Get.find<LoggerService>().log(
          "Terjadi error saat fetch API, mencoba mengambil dari cache: $e",
        );

        FirestoreServices firestoreServices;
        try {
          firestoreServices = Get.find<FirestoreServices>();
        } catch (_) {
          firestoreServices = Get.put(FirestoreServices());
        }

        final cachedData = await firestoreServices.getApiCallData(url: url);

        if (cachedData != null) {
          Get.find<LoggerService>().log(
            "Berhasil menggunakan data dari cache setelah error",
          );
          return controller.loadSuccess(
            requestCode: code,
            response: cachedData,
            statusCode: 200,
          );
        }
      } catch (cacheError) {
        Get.find<LoggerService>().log(
          "Gagal mengambil data dari cache setelah error: $cacheError",
        );
      }

      return controller.loadError(e);
    }
  }

  Future<void> apiPatch({
    required String url,
    required BaseControllers controller,
    required Map data,
    int code = 1,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      // Check internet connection first
      final hasConnection = await _checkInternetConnection();

      if (!hasConnection) {
        // No internet connection
        Get.find<LoggerService>().log("tidak ada koneksi");
        return controller.loadError(
          "No internet connection. Cannot perform update operation offline.",
        );
      }

      _print(doPrint: debug, data: url);
      _print(doPrint: debug, data: data);
      final response = await patch(url, data);
      _print(doPrint: debug, data: response.body ?? {});

      if (response.status.hasError) {
        _print(doPrint: debug, data: 'Error : $url');
        if (response.statusCode == 401) {
          Utils.setLoggedOut();
        }
        Get.snackbar(
          'Failed',
          '${response.body['error_description'] ?? '-'}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
        return controller.loadFailed(requestCode: code, response: response);
      }

      // We don't store PATCH responses in Firestore

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log(e.toString());
      return controller.loadError(e);
    }
  }

  Future<void> apiPost({
    required String url,
    required BaseControllers controller,
    required var data,
    int code = 1,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      // Check internet connection first
      final hasConnection = await _checkInternetConnection();

      if (!hasConnection) {
        // No internet connection
        Get.find<LoggerService>().log("tidak ada koneksi");
        return controller.loadError(
          "No internet connection. Cannot perform create operation offline.",
        );
      }

      _print(doPrint: debug, data: url);
      final response = await post(
        url,
        data,
      ).timeout(const Duration(seconds: 30));
      _print(doPrint: debug, data: data);
      _print(doPrint: debug, data: response.body ?? {});

      if (!response.isOk) {
        controller.setLoading(false);
        return controller.loadFailed(requestCode: code, response: response);
      }

      if (response.status.hasError) {
        _print(doPrint: debug, data: 'Error : $url');
        controller.setLoading(false);

        if (response.statusCode == 401) {
          if (code != kReqLogin) {
            Utils.setLoggedOut();
          } else {
            return controller.loadFailed(requestCode: code, response: response);
          }
        }
        Get.snackbar(
          'Failed',
          '${response.body['error_description'] ?? '-'}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
        return controller.loadFailed(requestCode: code, response: response);
      }

      // We don't store POST responses in Firestore

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log(e.toString());
      return controller.loadError('here $e');
    }
  }

  Future<void> apiDelete({
    required String url,
    required BaseControllers controller,
    required Map<String, dynamic> data,
    int code = 1,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      // Check internet connection first
      final hasConnection = await _checkInternetConnection();

      if (!hasConnection) {
        // No internet connection
        Get.find<LoggerService>().log("tidak ada koneksi");
        return controller.loadError(
          "No internet connection. Cannot perform delete operation offline.",
        );
      }

      _print(doPrint: debug, data: url);
      final response = await delete(url, query: data);

      _print(doPrint: debug, data: response.body ?? {});

      if (response.status.hasError) {
        _print(doPrint: debug, data: 'Error : $url');
        if (response.statusCode == 401) {
          Utils.setLoggedOut();
        }
        Get.snackbar(
          'Failed',
          '${response.body['error_description'] ?? '-'}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
        return controller.loadFailed(requestCode: code, response: response);
      }

      // We don't store DELETE responses in Firestore

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log(e.toString());
      return controller.loadError(e);
    }
  }

  void _print({bool doPrint = false, dynamic data}) {
    if (doPrint) {
      try {
        try {
          Get.find<LoggerService>().log(json.encode(data));
        } catch (_) {
          log(json.encode(data)); // Fallback to direct log
        }
      } catch (e) {
        try {
          Get.find<LoggerService>().log(data.toString());
        } catch (_) {
          log(data.toString()); // Fallback to direct log
        }
      }
    }
  }

  /// Store API call data in Firestore
  Future<void> _storeApiCallData({
    required String url,
    required dynamic responseData,
  }) async {
    try {
      FirestoreServices firestoreServices;
      try {
        firestoreServices = Get.find<FirestoreServices>();
      } catch (e) {
        Get.find<LoggerService>().log(
          'FirestoreServices tidak ditemukan, membuat instance baru',
        );
        firestoreServices = Get.put(FirestoreServices());
      }

      // Verifikasi bahwa responseData tidak null
      if (responseData == null) {
        Get.find<LoggerService>().log(
          'Warning: Attempting to store null responseData for URL: $url',
        );
        return;
      }

      // Log tipe data yang akan disimpan
      Get.find<LoggerService>().log(
        'Storing data of type ${responseData.runtimeType} for URL: $url',
      );

      // Simpan data ke Firestore
      await firestoreServices.storeApiCallData(
        url: url,
        responseData: responseData,
      );

      // Verifikasi bahwa data telah disimpan dengan mencoba mengambilnya kembali
      try {
        final verifyData = await firestoreServices.getApiCallData(url: url);
        if (verifyData != null) {
          Get.find<LoggerService>().log(
            'Verified data was stored successfully for URL: $url',
          );
        } else {
          Get.find<LoggerService>().log(
            'Warning: Could not verify data was stored for URL: $url',
          );
        }
      } catch (verifyError) {
        Get.find<LoggerService>().log(
          'Error verifying stored data: $verifyError',
        );
      }
    } catch (e) {
      Get.find<LoggerService>().log('Error storing API call data: $e');
    }
  }
}
