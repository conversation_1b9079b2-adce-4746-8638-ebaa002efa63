import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/login/login_header.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

// ignore: must_be_immutable
class BaseDetailPage extends StatelessWidget {
  final Widget child;
  final String title;
  final Function()? bottomAction;
  final String? bottomText;
  final BaseControllers controller;
  bool? backEnabled;
  final Function() onRefresh;
  bool? isActionActive;
  final Widget? bottomWidget;
  BaseDetailPage({
    super.key,
    required this.child,
    required this.title,
    required this.controller,
    required this.onRefresh,
    this.backEnabled,
    this.bottomAction,
    this.bottomText,
    this.isActionActive,
    this.bottomWidget,
  });

  @override
  Widget build(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return Scaffold(
      resizeToAvoidBottomInset: true,
      bottomNavigationBar:
          bottomAction != null || bottomWidget != null
              ? Container(
                width: Get.width,
                height: 70,

                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromRGBO(149, 157, 165, 0.2),
                      blurRadius: 24,
                      spreadRadius: 0,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child:
                      bottomWidget ??
                      PdlButton(
                        controller: controller,
                        onPressed: bottomAction,
                        title: bottomText ?? 'button_save'.tr,
                      ),
                ),
              )
              : null,
      body: isWideScreen ? _layoutWeb(context) : _layoutMobile(context),
    );
  }

  Widget _layoutMobile(context) {
    return Stack(
      children: [
        LoginHeader(
          child: Container(
            width: Get.width,
            height: 200,
            padding: EdgeInsets.symmetric(horizontal: paddingMedium),
            margin: EdgeInsets.only(top: 50),
            alignment: Alignment.topCenter,
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Get.back(),
                  child: Icon(Icons.arrow_back, color: Colors.white),
                ),
                if (isActionActive == true) SizedBox(width: paddingSmall),
                Expanded(
                  child: Text(
                    title,
                    textAlign: isActionActive == true ? null : TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (isActionActive == true)
                  Row(
                    children: [
                      Container(
                        width: 38,
                        height: 38,
                        decoration: BoxDecoration(
                          color: kColorBgLight.withValues(alpha: 0.35),
                          borderRadius: BorderRadius.circular(35),
                        ),
                      ),
                      SizedBox(width: paddingSmall),
                      Container(
                        width: 38,
                        height: 38,
                        decoration: BoxDecoration(
                          color: kColorBgLight.withValues(alpha: 0.35),
                          borderRadius: BorderRadius.circular(35),
                        ),
                      ),
                    ],
                  ),
                if (isActionActive != true)
                  Icon(Icons.arrow_back, color: Colors.transparent),
              ],
            ),
          ),
        ),
        SizedBox(
          width: Get.width,
          height: Get.height,
          child: Column(
            children: [
              SizedBox(height: 100),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async => onRefresh(),
                  child: Container(
                    height: Get.height,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: SingleChildScrollView(
                      physics: AlwaysScrollableScrollPhysics(),
                      child: child,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: Get.width, height: Get.height),
      ],
    );
  }

  Widget _layoutWeb(context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Utils.cachedImageWrapper(
            'image/img-bg-web.png',
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(
          width: Get.width,
          height: Get.height,
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical: paddingLarge,
                  horizontal: paddingLarge,
                ),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: backEnabled == true ? () => Get.back() : null,
                      child: Icon(
                        Icons.arrow_back,
                        color:
                            backEnabled == true
                                ? Colors.white
                                : Colors.transparent,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        title,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                    Icon(Icons.arrow_back, color: Colors.transparent),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  color: Theme.of(context).colorScheme.surface,
                  width: Get.width,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        SizedBox(width: Get.width / 1.5, child: child),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
