import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_dialog_content.dart';
import 'package:pdl_superapp/controllers/profile/attachement_bottom_controller.dart';
import 'package:pdl_superapp/models/upload_document_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class AttachementBottomSheet extends StatelessWidget {
  final bool? isKtp;
  final bool? isKK;
  final bool? isBank;
  final Function(UploadDocumentModels) onFinish;
  AttachementBottomSheet({
    super.key,
    required this.onFinish,
    this.isKtp,
    this.isBank,
    this.isKK,
  });

  final AttachementBottomController controller = Get.put(
    AttachementBottomController(),
  );

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(maxHeight: Get.height / 1.35, minHeight: 200),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: paddingMedium),
            Text(
              'subtitle_support_document'.tr,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: paddingSmall),
            if (isKtp == true)
              _buildSection(
                context,
                'title_new_ktp'.tr,
                controller.ktpFile,
                controller.indicatorKtp,
                'ktp',
              ),
            if (isKK == true)
              _buildSection(
                context,
                'title_new_kk'.tr,
                controller.kkFile,
                controller.indicatorKK,
                'kk',
              ),
            if (isBank == true)
              _buildSection(
                context,
                'title_new_bank_number'.tr,
                controller.bankFile,
                controller.indicatorBank,
                'bank',
              ),
            SizedBox(height: paddingLarge),
            PdlButton(
              onPressed: () {
                UploadDocumentModels data = UploadDocumentModels(
                  kkUrl: controller.kkURl.value,
                  ktpUrl: controller.ktpUrl.value,
                  bankUrl: controller.bankUrl.value,
                );
                onFinish(data);
                Get.back();
              },
              title: 'label_send_document'.tr,
              width: Get.width,
              controller: controller,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    Rx<XFile?> file,
    RxDouble indicator,
    String type,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _title(context, title),
        Obx(() => _imageContainer(context, file.value, indicator, type)),
      ],
    );
  }

  Widget _imageContainer(
    BuildContext context,
    XFile? image,
    RxDouble uploadIndicator,
    String type,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingSmall),
      child:
          image != null
              ? _uploadedImageView(context, image, uploadIndicator, type)
              : _uploadButton(context, type),
    );
  }

  Widget _uploadedImageView(
    BuildContext context,
    XFile image,
    RxDouble uploadIndicator,
    String type,
  ) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => _showDeleteDialog(context, type),
          child: Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              border: Border.all(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Image.file(
                  File(image.path),
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                ),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        image.name,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        Utils.getFileSize(image.path, 1).toString(),
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Icon(Icons.delete, color: Theme.of(context).colorScheme.error),
              ],
            ),
          ),
        ),
        if (uploadIndicator.value > 0.0 && uploadIndicator.value < 100.0)
          Positioned.fill(
            child: Center(
              child: LinearProgressIndicator(value: uploadIndicator.value),
            ),
          ),
      ],
    );
  }

  Widget _uploadButton(BuildContext context, String type) {
    return GestureDetector(
      onTap:
          () => Utils.imagePicker(
            context,
            onSuccess: (XFile image) => _updateImage(type, image),
          ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DottedBorder(
            radius: Radius.circular(8),
            borderType: BorderType.RRect,
            color: Get.isDarkMode ? kColorBorderDark : kColorTextDark,
            dashPattern: [8, 8],
            child: Container(
              width: Get.width,
              padding: EdgeInsets.all(paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(
                    Icons.image,
                    color: Theme.of(context).colorScheme.primary,
                    size: 32,
                  ),
                  Text(
                    'title_upload_photo'.tr,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: paddingSmall),
            child: Text(
              'label_image_warning'.tr,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  void _updateImage(String type, XFile image) {
    switch (type) {
      case 'ktp':
        controller.ktpFile.value = image;
        controller.performUpdatePicture(image: image, type: 'ktp');
        break;
      case 'kk':
        controller.kkFile.value = image;
        controller.performUpdatePicture(image: image, type: 'kk');
        break;
      case 'bank':
        controller.bankFile.value = image;
        controller.performUpdatePicture(image: image, type: 'bank');
        break;
    }
  }

  void _showDeleteDialog(BuildContext context, String type) {
    PdlBaseDialog(
      context: context,
      child: PdlDialogContent(
        message: 'title_delete_attachement'.trParams({
          'item': type.toUpperCase(),
        }),
        onTap: () {
          Get.back();
          _deleteImage(type);
        },
      ),
    );
  }

  void _deleteImage(String type) {
    switch (type) {
      case 'ktp':
        controller.ktpFile.value = null;
        controller.ktpUrl.value = '';
        controller.indicatorKtp.value = 0.0;
        break;
      case 'kk':
        controller.kkFile.value = null;
        controller.kkURl.value = '';
        controller.indicatorKK.value = 0.0;
        break;
      case 'bank':
        controller.bankFile.value = null;
        controller.bankUrl.value = '';
        controller.indicatorBank.value = 0.0;
        break;
    }
  }

  Widget _title(BuildContext context, String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: paddingMedium),
      child: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
      ),
    );
  }
}
