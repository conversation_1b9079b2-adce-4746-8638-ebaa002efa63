import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';

enum ChipType { success, danger, warning, info }

class CustomChip extends StatelessWidget {
  final ChipType type;
  final String text;

  const CustomChip({super.key, required this.type, required this.text});

  @override
  Widget build(BuildContext context) {
    Color color;
    switch (type) {
      case ChipType.success:
        color = chipColorSuccess;
        break;
      case ChipType.danger:
        color = chipColorDanger;
        break;
      case ChipType.info:
        color = chipColorInfo;
        break;
      case ChipType.warning:
        color = chipColorWarning;
        break;
    }
    return Container(
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(radiusSmall),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: paddingSmall,
          horizontal: paddingSmall,
        ),
        child: Text(text, style: TextStyle(color: color)),
      ),
    );
  }
}
