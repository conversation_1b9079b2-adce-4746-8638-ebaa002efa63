import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/icon_menu.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';

class HomeMenu extends StatelessWidget {
  const HomeMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: GridView.count(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        crossAxisCount: 4,
        children: [
          for (int i = 0; i < 6; i++)
            IconMenu(
              onTap: () => Get.toNamed(Routes.MENU_KEAGENAN),
              iconUrl: 'icon/ic-menu-keagenan.svg',
              title: 'Keagenan',
            ),
        ],
      ),
    );
  }
}
