import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_accordion.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class HomeWidgetBase extends StatelessWidget {
  final String title;
  final String iconUrl;
  final Widget content;
  final String? widgetKey;
  const HomeWidgetBase({
    super.key,
    required this.title,
    required this.iconUrl,
    required this.content,
    this.widgetKey,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      margin: EdgeInsets.only(
        left: paddingMedium,
        right: paddingMedium,
        bottom: paddingLarge,
      ),
      padding: EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.1),
            blurRadius: 10, // Softness of the shadow
            spreadRadius: 0, // How much the shadow spreads
            offset: Offset(0, 0), // Shadow position
          ),
        ],
      ),
      child: PdlAccordion(
        title: SizedBox(
          width: Get.width,
          child: Wrap(
            spacing: paddingMedium,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              Utils.cachedSvgWrapper(iconUrl, width: 42, height: 42),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
              ),
            ],
          ),
        ),
        widgets: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [SizedBox(height: paddingSmall), Divider(), content],
        ),
        defaultShow: true,
      ),
    );
  }
}
