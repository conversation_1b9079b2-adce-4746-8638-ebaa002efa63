import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';

class WidgetLoadMore extends StatelessWidget {
  final Function() onTap;
  const WidgetLoadMore({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.refresh),
          const SizedBox(width: paddingSmall),
          const Text("Perbarui"),
        ],
      ),
    );
  }
}
