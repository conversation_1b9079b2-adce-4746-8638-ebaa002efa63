import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/widget/home_widget_spaj_controller.dart';
import 'package:pdl_superapp/models/widget/widget_spaj_models.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class HomeWidgetSpaj extends StatelessWidget {
  HomeWidgetSpaj({super.key});

  final HomeWidgetSpajController controller = Get.put(
    HomeWidgetSpajController(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.hasError.isTrue) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 8),
              Text('Error: ${controller.errorMessage.value}'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => controller.load(),
                child: const Text('Coba Lagi'),
              ),
            ],
          ),
        );
      }

      return Column(
        children: [
          // Show tabs for BM role
          if (controller.userLevel != kLevelBP) _buildTabSelector(),

          // Content based on role and selected tab
          _buildContent(context),

          WidgetLoadMore(onTap: () => controller.load()),
        ],
      );
    });
  }

  // Tab selector for BM role
  Widget _buildTabSelector() {
    return Container(
      margin: const EdgeInsets.only(bottom: paddingMedium),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(200),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToIndividu(),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        controller.selectedSection.value == 0
                            ? Colors.white
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(200),
                    border: Border.all(
                      color:
                          controller.selectedSection.value == 0
                              ? Colors.grey
                              : Colors.transparent,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Individu',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                      color:
                          controller.selectedSection.value == 0
                              ? Colors.black
                              : Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToTeam(),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        controller.selectedSection.value == 1
                            ? Colors.white
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(200),
                    border: Border.all(
                      color:
                          controller.selectedSection.value == 1
                              ? Colors.grey
                              : Colors.transparent,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Team',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                      color:
                          controller.selectedSection.value == 1
                              ? Colors.black
                              : Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent(context) {
    return Obx(() {
      if (controller.arrData.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Text('Tidak ada data SPAJ'),
          ),
        );
      }

      return Column(
        children: [
          for (int i = 0; i < controller.arrData.length; i++)
            _card(
              context,
              spajData: controller.arrData[i],
              isLast: i == controller.arrData.length - 1,
            ),
          SizedBox(
            width: double.infinity,
            child: PdlButton(
              title: "Lihat Detail",
              onPressed: () => Get.toNamed(Routes.SPAJ),
            ),
          ),
        ],
      );
    });
  }

  Widget _card(context, {required WidgetSpajModels spajData, bool? isLast}) {
    return Container(
      padding: EdgeInsets.only(top: paddingSmall),
      width: Get.width,
      child: Column(
        children: [
          if (controller.userLevel != kLevelBP)
            Padding(
              padding: EdgeInsets.only(bottom: paddingSmall),
              child: Row(
                children: [
                  CircleAvatar(
                    child: Center(child: Text(spajData.agentName?[0] ?? '-')),
                  ),
                  SizedBox(width: paddingSmall),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          spajData.agentName ?? '-',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w700),
                        ),
                        Text(
                          spajData.agentCode ?? '-',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          _cardTitle(context, spajData: spajData),
          SizedBox(height: paddingSmall),
          _cardContent(context, spajData: spajData),
          if (isLast != true) Divider(),
        ],
      ),
    );
  }

  Padding _cardContent(context, {required WidgetSpajModels spajData}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingSmall),
      child: Column(
        children: [
          _contentField(
            context,
            title: 'Pemegang Polis',
            value: spajData.policyHolderName ?? '-',
          ),
          _contentField(
            context,
            title: 'Premi',
            value: Utils.currencyFormatters(
              data: spajData.basicPremium.toString(),
              currency: spajData.currency,
            ),
          ),
          _contentField(
            context,
            title: 'Periode Pembayaran',
            value: spajData.frequency ?? '-',
          ),
          if (spajData.spajStatus != kSpajStatAccept)
            _contentField(
              context,
              title: 'Tanggal Submit',
              value: DateFormat(
                'MM/dd/yy',
              ).format(DateTime.parse(spajData.submitDate!)),
            ),
        ],
      ),
    );
  }

  Row _contentField(context, {required String title, required String value}) {
    return Row(
      children: [
        Expanded(child: Text(title)),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
      ],
    );
  }

  Row _cardTitle(context, {required WidgetSpajModels spajData}) {
    return Row(
      children: [
        Expanded(
          child: Text(
            spajData.spajNumber ?? '-',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color:
                spajData.spajStatus == kSpajStatAccept
                    ? kColorGlobalBgGreen
                    : kColorGlobalBgWarning,
            borderRadius: BorderRadius.circular(8),
          ),
          padding: EdgeInsets.all(paddingExtraSmall),
          child: Text(
            spajData.spajStatus ?? '-',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color:
                  spajData.spajStatus == kSpajStatAccept
                      ? kColorGlobalGreen
                      : kColorGlobalWarning,
            ),
          ),
        ),
      ],
    );
  }
}
