import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class IconMenu extends StatelessWidget {
  final Function() onTap;
  final String iconUrl;
  final String title;
  const IconMenu({
    required this.onTap,
    required this.iconUrl,
    required this.title,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 52,
              height: 52,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(52),
              ),
              child: Utils.cachedSvgWrapper(iconUrl, width: 52, height: 52),
            ),
            SizedBox(height: paddingSmall),
            Text(title, textAlign: TextAlign.center),
          ],
        ),
      ),
    );
  }
}
