import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/flyer_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class NormalCarouselController extends BaseControllers {
  CarouselSliderController carouselSliderController =
      CarouselSliderController();

  RxInt currentPage = 0.obs;

  onPageChanged(int index, CarouselPageChangedReason reason) {
    currentPage.value = index;
  }
}

class NormalCarousel extends StatelessWidget {
  final List<FlyerModels> arrData;
  final Function(FlyerModels) onPressed;

  final NormalCarouselController controller = Get.put(
    NormalCarouselController(),
  );
  NormalCarousel({super.key, required this.onPressed, required this.arrData});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: Get.width,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
          // height: 174,
          clipBehavior: Clip.hardEdge,
          child: CarouselSlider(
            carouselController: controller.carouselSliderController,
            items: [for (int i = 0; i < arrData.length; i++) _card(arrData[i])],
            options: CarouselOptions(
              autoPlay: true,
              viewportFraction: 1,
              aspectRatio: 21 / 9,
              onPageChanged:
                  (index, reason) => controller.onPageChanged(index, reason),
            ),
          ),
        ),
        Positioned(
          bottom: 26,
          left: 47,
          child: Obx(
            () => AnimatedSmoothIndicator(
              activeIndex: controller.currentPage.value,
              count: arrData.isNotEmpty ? arrData.length : 1,
              effect: ExpandingDotsEffect(
                dotColor: Color(0xFFF6F6F6),
                activeDotColor: Colors.white,
                dotHeight: 8,
                dotWidth: 8,
                spacing: 4,
                expansionFactor: 4,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _card(FlyerModels data) {
    return GestureDetector(
      onTap: () => onPressed(data),
      child: Container(
        width: Get.width,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(16)),
        clipBehavior: Clip.hardEdge,
        margin: EdgeInsets.symmetric(horizontal: paddingMedium),
        height: 174,
        child: CachedNetworkImage(
          imageUrl: data.frontImage!,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
