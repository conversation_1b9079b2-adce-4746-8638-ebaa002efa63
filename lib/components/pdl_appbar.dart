import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PdlAppbar extends AppBar {
  final String? titles;
  final Color? bgColor;
  PdlAppbar({super.key, this.titles, this.bgColor})
    : super(
        backgroundColor:
            titles != null
                ? (Get.isDarkMode ? kColorBgDark : kColorBgLight)
                : bgColor ?? Colors.transparent,
        title:
            titles != null
                ? Container(
                  width: Get.width,
                  alignment: Alignment.center,
                  child: Text(
                    titles,
                    style: Theme.of(
                      Get.context!,
                    ).textTheme.titleMedium?.copyWith(
                      color: kColorBgLight,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                )
                : Container(
                  width: Get.width,
                  padding: EdgeInsets.symmetric(horizontal: paddingExtraSmall),
                  child: Row(
                    children: [
                      SizedBox(height: 30, child: Image.asset(kIconImage)),
                      Expanded(child: Container()),
                      Container(
                        width: 38,
                        height: 38,
                        decoration: BoxDecoration(
                          color: kColorBgLight.withValues(alpha: 0.35),
                          borderRadius: BorderRadius.circular(35),
                        ),
                      ),
                      SizedBox(width: paddingSmall),
                      Container(
                        width: 38,
                        height: 38,
                        decoration: BoxDecoration(
                          color: kColorBgLight.withValues(alpha: 0.35),
                          borderRadius: BorderRadius.circular(35),
                        ),
                      ),
                    ],
                  ),
                ),

        flexibleSpace:
            titles == null
                ? null
                : Stack(
                  children: [
                    SizedBox(
                      width: Get.width,
                      height: 75,
                      child: Utils.cachedImageWrapper(
                        'image/img-bg-web.png',
                        fit: BoxFit.cover,
                        alignment: Alignment.topCenter,
                      ),
                    ),
                  ],
                ),
      );
}
