import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

class PdlBaseDialog {
  BuildContext context;
  Widget child;
  bool? bgIsBlurred;
  String? title;
  PdlBaseDialog({
    Color? bgColor,
    required this.context,
    required this.child,
    this.bgIsBlurred,
    this.title,
  }) {
    final bool isWideScreen = kIsWeb;
    Get.dialog(
      _blurBackground(
        child: Material(
          color: Colors.transparent,
          child: Container(
            width: isWideScreen ? Get.width / 3 : Get.width,
            padding: EdgeInsets.all(paddingLarge),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: Theme.of(context).colorScheme.surface,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (title != null)
                  Container(
                    width: Get.width,
                    padding: EdgeInsets.only(bottom: paddingMedium),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            title!,
                            style: Theme.of(context).textTheme.bodyLarge
                                ?.copyWith(fontWeight: FontWeight.w700),
                          ),
                        ),
                        GestureDetector(
                          onTap: () => Get.back(),
                          child: Container(
                            color: Colors.transparent,
                            child: Icon(Icons.close),
                          ),
                        ),
                      ],
                    ),
                  ),
                child,
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: true,
    );
  }

  _blurBackground({required Widget child}) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: paddingMedium),
        child:
            bgIsBlurred != true
                ? child
                : ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                    child: Container(
                      padding: EdgeInsets.all(paddingMedium),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.surface.withValues(alpha: 0.4),
                        border: Border.all(color: Color(0x404B6980)),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: child,
                    ),
                  ),
                ),
      ),
    );
  }
}
