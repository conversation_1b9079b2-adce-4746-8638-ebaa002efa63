import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/utils/constants.dart';

class PdlBottomSheet {
  final String title;
  final Widget content;
  final bool? noPadding;
  final bool? dismissable;
  PdlBottomSheet({
    required this.content,
    required this.title,
    this.noPadding,
    this.dismissable,
  }) {
    Get.bottomSheet(
      Container(
        width: Get.width,
        padding: EdgeInsets.symmetric(
          horizontal: noPadding != true ? paddingMedium : 0,
          vertical: noPadding != true ? paddingLarge : 0,
        ),
        constraints: BoxConstraints(
          maxHeight: Get.height / 1.2,
          minHeight: 200,
        ),
        decoration: BoxDecoration(
          color: Theme.of(Get.context!).colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                // horizontal: paddingSmall,
                // vertical: paddingLarge,
              ),

              child: Row(
                children: [
                  Expanded(child: TitleWidget(title: title)),
                  dismissable == false
                      ? Container()
                      : GestureDetector(
                        onTap: () => Get.back(),
                        child: Icon(Icons.close),
                      ),
                ],
              ),
            ),
            content,
          ],
        ),
      ),
      isScrollControlled: true,
      enableDrag: dismissable ?? true,
      isDismissible: dismissable ?? true,
    );
  }
}
