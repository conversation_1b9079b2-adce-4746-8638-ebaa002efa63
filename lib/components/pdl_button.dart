import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PdlButton extends StatelessWidget {
  final String? title;
  final double? width;
  final BaseControllers? controller;
  final Function()? onPressed;
  final double? radius;
  final Color? backgroundColor;
  final Color? foregorundColor;
  final Color? borderColor;
  const PdlButton({
    super.key,
    this.title,
    this.onPressed,
    this.width,
    this.radius,
    this.backgroundColor,
    this.foregorundColor,
    this.borderColor,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      child: FilledButton(
        onPressed:
            controller?.state.value == ControllerState.loading
                ? null
                : onPressed,
        style: FilledButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregorundColor,
          disabledBackgroundColor:
              Get.isDarkMode ? kColorTextTersier : kColorBorderLight,
          disabledForegroundColor: Color(0xFF6D6D6D),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radius ?? 8),
            side: BorderSide(color: borderColor ?? Colors.transparent),
          ),
        ),
        child:
            controller?.state.value == ControllerState.loading
                ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(),
                )
                : Text(title ?? ''),
      ),
    );
  }
}
