import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:showcaseview/showcaseview.dart';

class PdlShowcase extends StatelessWidget {
  final GlobalKey globalKey;
  final Widget child;
  final double? radius;

  const PdlShowcase({
    super.key,
    required this.globalKey,
    required this.child,
    this.radius,
  });

  @override
  Widget build(BuildContext context) {
    return Showcase.withWidget(
      key: globalKey,
      height: 200,
      width: Get.width - paddingExtraLarge,
      tooltipPosition: TooltipPosition.bottom,
      disableMovingAnimation: true,
      targetBorderRadius: BorderRadius.circular(radius ?? 8),
      targetPadding: EdgeInsets.all(8),
      toolTipSlideEndDistance: 0,
      container: Builder(
        builder: (context) {
          // Calculate the position of the arrow based on the global key
          final RenderBox? renderBox =
              globalKey.currentContext?.findRenderObject() as RenderBox?;
          final position = renderBox?.localToGlobal(Offset.zero);
          final size = renderBox?.size;

          // Default arrow position (center) if can't determine
          double arrowLeftPosition = Get.width / -10;

          // If we can get the target position, center the arrow under it
          if (position != null && size != null) {
            // Calculate the center of the target widget
            final targetCenter = position.dx + (size.width / 2);
            // Adjust for the tooltip container margin
            arrowLeftPosition = targetCenter - paddingMedium - 15;
          }

          return Stack(
            clipBehavior: Clip.none,
            children: [
              // Main content container
              Container(
                margin: EdgeInsets.only(top: 10),
                width: Get.width - (paddingSmall * 2),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: EdgeInsets.only(
                    left: paddingMedium,
                    right: paddingMedium,
                    top: paddingMedium,
                    bottom: paddingSmall,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Title',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Text(
                        'Content',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      SizedBox(height: paddingLarge),
                      Row(
                        children: [
                          Expanded(child: Text('1/6')),
                          PdlButton(
                            radius: 24,
                            backgroundColor:
                                Theme.of(context).colorScheme.surface,
                            foregorundColor:
                                Theme.of(context).colorScheme.primary,
                            borderColor: Theme.of(context).colorScheme.primary,
                            title: 'Kembali',
                            onPressed: () {},
                          ),
                          SizedBox(width: paddingSmall),
                          PdlButton(
                            radius: 24,
                            title: 'Lanjut',
                            onPressed: () {},
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Triangle arrow pointing up to the target widget
              Positioned(
                top: 0,
                left: arrowLeftPosition,
                child: ClipPath(
                  clipper: TriangleClipper(),
                  child: Container(width: 20, height: 10, color: Colors.white),
                ),
              ),
            ],
          );
        },
      ),
      child: child,
    );
  }
}

class TriangleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
