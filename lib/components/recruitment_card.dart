import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RecruitmentCard extends StatelessWidget {
  final RecruitmentFormModel form;
  final Function()? onTap;

  const RecruitmentCard({super.key, required this.form, this.onTap});

  @override
  Widget build(BuildContext context) {
    // Ambil nama dari form atau tampilkan placeholder jika kosong
    final String name =
        form.namaKtp?.isNotEmpty == true ? form.namaKtp! : 'Kandidat Baru';

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: Get.width,
        color: Colors.transparent,
        padding: EdgeInsets.only(
          left: paddingMedium,
          right: paddingMedium,
          top: paddingMedium,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar dengan inisial nama
                CircleAvatar(
                  backgroundColor: kColorGlobalBlue,
                  child: Text(
                    Utils.getInitials(name),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              name,
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                            Text(
                              form.candidateBranch ?? '-',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                            SizedBox(height: paddingMedium),
                            Row(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: kColorGlobalBgBlue,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: EdgeInsets.all(paddingExtraSmall),
                                  child: Text(
                                    'Draft',
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(color: kColorGlobalBlue),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      CircleAvatar(
                        backgroundColor: kColorGlobalBgBlue,
                        child: Icon(
                          Icons.chevron_right_outlined,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingSmall),
            Divider(),
          ],
        ),
      ),
    );
  }
}
