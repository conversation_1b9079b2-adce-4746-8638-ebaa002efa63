import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';

enum Status { diproses, disetujui, ditolak }

class StatusLabel extends StatelessWidget {
  const StatusLabel({super.key, required this.status});
  final Status status;

  @override
  Widget build(BuildContext context) {
    Color bgColor, textColor;

    switch (status) {
      case Status.disetujui:
        bgColor = kColorGlobalBgGreen;
        textColor = kColorGlobalGreen;
        break;
      case Status.ditolak:
        bgColor = kColorGlobalBgRed;
        textColor = kColorGlobalRed;
        break;
      default:
        bgColor = kColorGlobalBgBlue;
        textColor = kColorPaninBlue;
    }

    return Container(
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.all(paddingExtraSmall),
      child: Text(
        status.name.toCapitalize(),
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(color: textColor),
      ),
    );
  }
}
