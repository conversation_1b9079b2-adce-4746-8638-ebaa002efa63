import 'package:flutter/material.dart';
import '../utils/constants.dart';

class TableCard extends StatelessWidget {
  final String? title;
  final List<String> headers;
  final List<List<String>> rows;
  final double progress;
  final Map<int, FlexColumnWidth>? columnWidths;
  final Map<int, FlexColumnWidth>? headerColumnWidths;
  final List<int>? boldRows; // Indices of rows that should be bold
  final Map<int, double>? minColumnWidths; // Minimum width for each column
  final bool
  horizontalScrollable; // Whether the table should be horizontally scrollable

  const TableCard({
    super.key,
    this.title,
    required this.headers,
    required this.rows,
    this.progress = 0.0,
    this.columnWidths,
    this.headerColumnWidths,
    this.boldRows,
    this.minColumnWidths,
    this.horizontalScrollable = false,
  });

  // Generate column widths based on the provided parameter or use default values
  Map<int, TableColumnWidth> _getColumnWidths() {
    // Default column widths (equal distribution)
    Map<int, TableColumnWidth> defaultWidths = {};

    // Create default widths based on the number of headers
    for (int i = 0; i < headers.length; i++) {
      if (minColumnWidths != null && minColumnWidths!.containsKey(i)) {
        // Use FixedColumnWidth with min width constraint if specified
        double minWidth =
            minColumnWidths![i] ?? 100.0; // Default to 100.0 if null
        defaultWidths[i] = FixedColumnWidth(minWidth);
      } else {
        defaultWidths[i] = const FlexColumnWidth(1);
      }
    }

    // Apply custom column widths if provided
    if (columnWidths != null) {
      columnWidths!.forEach((key, value) {
        // If min width is specified, use the larger of the two
        if (minColumnWidths != null && minColumnWidths!.containsKey(key)) {
          double minWidth =
              minColumnWidths![key] ?? 100.0; // Default to 100.0 if null
          defaultWidths[key] = FixedColumnWidth(minWidth);
        } else {
          defaultWidths[key] = value;
        }
      });
    }

    return defaultWidths;
  }

  // Generate header column widths based on the provided parameter or use default values
  Map<int, TableColumnWidth> _getHeaderColumnWidths() {
    // Start with the regular column widths
    Map<int, TableColumnWidth> headerWidths = Map.from(_getColumnWidths());

    // Apply header-specific widths if provided
    if (headerColumnWidths != null) {
      headerColumnWidths!.forEach((key, value) {
        // If min width is specified, use the larger of the two
        if (minColumnWidths != null && minColumnWidths!.containsKey(key)) {
          double minWidth =
              minColumnWidths![key] ?? 100.0; // Default to 100.0 if null
          headerWidths[key] = FixedColumnWidth(minWidth);
        } else {
          headerWidths[key] = value;
        }
      });
    }

    return headerWidths;
  }

  @override
  Widget build(BuildContext context) {
    // Create the table content
    Widget tableContent = Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radiusSmall),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Header row with potentially different column widths
          Table(
            columnWidths: _getHeaderColumnWidths(),
            children: [
              TableRow(
                decoration: BoxDecoration(
                  color: Color(0xFF0075BD),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(radiusSmall),
                    topRight: Radius.circular(radiusSmall),
                  ),
                ),
                children:
                    headers
                        .map(
                          (header) => Padding(
                            padding: const EdgeInsets.all(paddingSmall),
                            child: Text(
                              header,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        )
                        .toList(),
              ),
            ],
          ),

          // Content rows with potentially different column widths
          Table(
            columnWidths: _getColumnWidths(),
            children:
                rows.asMap().entries.map((entry) {
                  final int rowIndex = entry.key;
                  final List<String> row = entry.value;
                  final bool isBold = boldRows?.contains(rowIndex) ?? false;

                  return TableRow(
                    children:
                        row
                            .map(
                              (cell) => Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: paddingSmall,
                                ),
                                child: Text(
                                  cell,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontWeight:
                                        isBold
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                  );
                }).toList(),
          ),
        ],
      ),
    );

    // Wrap in horizontal scroll view if needed
    if (horizontalScrollable) {
      tableContent = SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: tableContent,
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (title != null)
          Text(title!, style: const TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: paddingSmall),
        tableContent,
      ],
    );
  }
}
