import 'package:flutter/widgets.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/encryption_service.dart';

class ForogotPasswordController extends BaseControllers {
  TextEditingController agentCodeTextController = TextEditingController();
  TextEditingController emailTextController = TextEditingController();
  RxBool isBtnAvailabel = false.obs;
  RxBool isInvalid = false.obs;

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    Get.offNamed(
      Routes.FORGOT_PASSWORD_SENT,
      arguments: {'email': response['email']},
    );
  }
  // http://superapp-nlb-73057e211678c433.elb.ap-southeast-3.amazonaws.com/
  // forget-password/
  // reset?
  // token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwMDAwODAyMSIsInJvbGVzIjpbIlJPTEVfQUdFX0JEIl0sImV4cCI6MTc0Mjg1ODA5MiwiaWF0IjoxNzQyODU3MTkyfQ.mV96m9Jggl2f-_A3Iikp5mkM51s0Qbt5T6gyCw5UyM779EunbXIl6zXYr9kjajKGSQUJkHiV22LyARJtXFHVmA

  // ? and = should be parsed
  // xcrun simctl openurl booted https://yourDomain.com/path

  // adb shell am start -a android.intent.action.VIEW \
  //   -c android.intent.category.BROWSABLE \
  //   -d https://nrwvafwa4f.execute-api.ap-southeast-3.amazonaws.com/dev/agent/forget-password/reset%3Ftoken%3DeyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwMDAwODAyMSIsInJvbGVzIjpbIlJPTEVfQUdFX0JEIl0sImV4cCI6MTc0MzA0NDc5MiwiaWF0IjoxNzQzMDQzODkyfQ.o5Xw4S8V7E5OkWwcSpfEK2WB8jo-RpUozjYoMuBFlQRDvnqOCMSwaqZSiuBvUBW2b9ywoYlfdr6P4PsCREU6ew \
  //   id.co.panindaiichilife.quantumx360

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    isInvalid.value = true;
  }

  onChangeText() {
    if (agentCodeTextController.text.isEmpty ||
        emailTextController.text.isEmpty) {
      isBtnAvailabel.value = false;
    } else {
      isBtnAvailabel.value = true;
    }
  }

  onTapNext() {
    performNext();
  }

  performNext() async {
    isInvalid.value = false;
    final secretKey = dotenv.get("PRIVATE_KEY");

    final username = Encryption(
      secretKey,
    ).doencrypt(agentCodeTextController.text, agentCodeTextController.text);

    final email = Encryption(
      secretKey,
    ).doencrypt(emailTextController.text, emailTextController.text);

    var data = {"username": username, "email": email};
    await api.performForgotPassword(controllers: this, data: data);
  }
}
