import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/flyer_models.dart';
import 'package:pdl_superapp/utils/encryption_service.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LoginController extends BaseControllers {
  final LocalAuthentication auth = LocalAuthentication();
  RxBool biometricAvailable = false.obs;

  TextEditingController usernameTextController = TextEditingController();
  TextEditingController passwordTextController = TextEditingController();

  RxBool isButtonAvailable = false.obs;
  RxBool isInvalid = false.obs;

  String encyrptedUsername = '';
  String encryptedPassword = '';

  // flyer
  RxList<FlyerModels> flyerArrData = RxList();

  @override
  void onInit() {
    super.onInit();
    getFlyer();
    if (!kIsWeb) {
      checkBiometrict();
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqLogin:
        Utils.setLoggedIn(
          token: response['accessToken'],
          refreshToken: response['refreshToken'],
          username: encyrptedUsername,
          password: encryptedPassword,
        );
        break;
      case kReqGetFlyer:
        parseFlyer(response);
        break;
      default:
    }
  }

  getTnc() {}
  getFlyer() async {
    await api.getFlyer(controllers: this, code: kReqGetFlyer);
  }

  parseFlyer(response) {
    flyerArrData.clear();

    for (int i = 0; i < response['content'].length; i++) {
      FlyerModels data = FlyerModels.fromJson(response['content'][i]);
      flyerArrData.add(data);
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    isInvalid.value = true;
  }

  checkBiometrict() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final bool canAuthenticateWithBiometrics = await auth.canCheckBiometrics;
    if (canAuthenticateWithBiometrics == true) {
      final List<BiometricType> availableBiometrics =
          await auth.getAvailableBiometrics();

      if (availableBiometrics.isNotEmpty) {
        biometricAvailable.value =
            prefs.getBool(kStorageIsBiometricActive) ?? false;
      } else {
        biometricAvailable.value = false;
      }
    }
  }

  emptValidator() {
    if (usernameTextController.text == '' ||
        passwordTextController.text == '') {
      isButtonAvailable.value = false;
    } else {
      isButtonAvailable.value = true;
    }
  }

  perform() async {
    isInvalid.value = false;
    final secretKey = dotenv.get("PRIVATE_KEY");

    final username = Encryption(
      secretKey,
    ).doencrypt(usernameTextController.text, usernameTextController.text);

    final password = Encryption(
      secretKey,
    ).doencrypt(passwordTextController.text, passwordTextController.text);
    setLoading(true);
    // store username/password encrypted to current state
    encyrptedUsername = username;
    encryptedPassword = password;

    await api.performLogin(
      controllers: this,
      data: {"password": password, "username": username},
      code: kReqLogin,
    );
  }

  performLoginBio() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    String username = prefs.getString(kStorageUsername) ?? '';
    String password = prefs.getString(kStoragePassword) ?? '';

    setLoading(true);
    await api.performLogin(
      controllers: this,
      data: {"password": password, "username": username},
      code: kReqLogin,
    );
  }
}
