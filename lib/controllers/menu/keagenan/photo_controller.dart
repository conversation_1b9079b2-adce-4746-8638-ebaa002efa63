import 'dart:io';
import 'package:camera/camera.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/routes/app_routes.dart';

class PdlPHotoController extends BaseControllers {
  // Camera controller
  Rx<CameraController?> cameraController = Rx<CameraController?>(null);
  RxBool isCameraInitialized = false.obs;
  RxBool isTakingPicture = false.obs;
  Rx<File?> capturedImage = Rx<File?>(null);
  String type = '';

  @override
  void onClose() {
    cameraController.value?.dispose();
    super.onClose();
  }

  Future<void> initializeCamera() async {
    try {
      // Get available cameras
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        Get.snackbar(
          'Error',
          'No cameras available',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // Use the first camera (usually the back camera)
      final camera = cameras.first;

      // Initialize the camera controller
      final controller = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      // Initialize the controller
      await controller.initialize();

      // Update the controller
      cameraController.value = controller;
      isCameraInitialized.value = true;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to initialize camera: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> takePicture() async {
    if (cameraController.value == null ||
        !isCameraInitialized.value ||
        isTakingPicture.value) {
      return;
    }

    try {
      isTakingPicture.value = true;

      // Take the picture
      final XFile photo = await cameraController.value!.takePicture();

      // Create a File from the XFile
      final File imageFile = File(photo.path);

      // Save the image to a permanent location
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String fileName = path.basename(photo.path);
      final File savedImage = await imageFile.copy('${appDir.path}/$fileName');

      // Update the captured image
      capturedImage.value = savedImage;

      isTakingPicture.value = false;

      // Navigate to the image cropper page
      final croppedImage = await Get.toNamed(
        Routes.PHOTO_IMAGE_CROPPER,
        arguments: {'imageFile': savedImage, 'type': type},
      );

      // If we got a cropped image back, return it
      if (croppedImage != null && croppedImage is File) {
        Get.back(result: croppedImage);
      }
    } catch (e) {
      isTakingPicture.value = false;
      Get.snackbar(
        'Error',
        'Failed to take picture: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<File?> cropImage(File imageFile) async {
    try {
      // Navigate to the image cropper page
      final croppedImage = await Get.toNamed(
        Routes.PHOTO_IMAGE_CROPPER,
        arguments: {'imageFile': imageFile, 'type': type},
      );

      // Return the cropped image if available
      if (croppedImage != null && croppedImage is File) {
        return croppedImage;
      }

      return null;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to crop image: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }
  }
}
