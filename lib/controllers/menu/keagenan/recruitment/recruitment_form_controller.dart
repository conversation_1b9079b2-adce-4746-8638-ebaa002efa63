import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/combo_box_models.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/form_firestore_service.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class RecruitmentFormController extends BaseControllers {
  PageController pageController = PageController();
  RxInt activePage = 0.obs;

  List<String> month = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'Mei',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Okt',
    'Nov',
    'Des',
  ];

  late SharedPreferences prefs;

  // Form ID untuk Firestore
  RxString formId = ''.obs;

  // Status form
  RxBool isFormLoaded = false.obs;
  RxBool isFormSaving = false.obs;
  RxBool isFormSubmitted = false.obs;
  RxString formStatus = 'draft'.obs;

  // Timer untuk auto-save
  Timer? _autoSaveTimer;
  final int _autoSaveIntervalSeconds = 5; // Auto-save setiap 5 detik

  // Service untuk Firestore
  final FormFirestoreService _firestoreService = FormFirestoreService();

  // Form Field data
  Rx<File?> ktpImage = Rx<File?>(null);
  Rx<File?> selfieKtpImage = Rx<File?>(null);
  Rx<File?> pasFotoImage = Rx<File?>(null);

  // Image URLs from upload
  RxString ktpUrl = ''.obs;
  RxString selfieKtpUrl = ''.obs;
  RxString pasFotoUrl = ''.obs;

  // Upload progress indicators
  RxDouble ktpUploadProgress = 0.0.obs;
  RxDouble selfieKtpUploadProgress = 0.0.obs;
  RxDouble pasFotoUploadProgress = 0.0.obs;

  // Upload status
  RxBool isKtpUploading = false.obs;
  RxBool isSelfieKtpUploading = false.obs;
  RxBool isPasFotoUploading = false.obs;

  // --- TextEditingControllers for Form Fields ---
  // Form Verification - menggunakan RxString untuk recruiter info
  final recruiterName = RxString('');
  final recruiterId = RxString('');
  final recruiterBranch = RxString('');
  final recruiterCode = RxString('');
  final recruiterLevel = RxString('');
  final recruiterPhoto = RxString('');
  final candidateLevelController = TextEditingController();
  final candidateBranchController = TextEditingController();

  // FormIdentification
  final nikController = TextEditingController();
  final namaKtpController = TextEditingController();
  final tempatLahirController = TextEditingController();
  final tanggalLahirController = TextEditingController();
  final bulanLahirController = TextEditingController();
  final tahunLahirController = TextEditingController();
  final jenisKelaminController = TextEditingController();
  final alamatKtpController = TextEditingController();
  final rtKtpController = TextEditingController();
  final rwKtpController = TextEditingController();
  final provinsiKtpController = TextEditingController();
  final kabupatenKtpController = TextEditingController();
  final kecamatanKtpController = TextEditingController();
  final kelurahanKtpController = TextEditingController();

  // Alamat Domisili (Home Address)
  final alamatDomisiliController = TextEditingController();
  final rtDomisiliController = TextEditingController();
  final rwDomisiliController = TextEditingController();
  final provinsiDomisiliController = TextEditingController();
  final kabupatenDomisiliController = TextEditingController();
  final kecamatanDomisiliController = TextEditingController();
  final kelurahanDomisiliController = TextEditingController();

  // FormSelfIdentification
  final emailController = TextEditingController();
  final nomorHpController = TextEditingController();
  // Emergency Contact
  final emergencyNamaController = TextEditingController();
  final emergencyHubunganController = TextEditingController();
  final emergencyNomorHpController = TextEditingController();
  // Bank
  final namaPemilikRekeningController = TextEditingController();
  final nomorRekeningController = TextEditingController();

  // Combo Box
  RxList<ComboBoxAgentLevel> agentLevelList = RxList();
  RxBool isAgentLoading = true.obs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    // Inisialisasi form ID atau ambil dari parameter jika ada
    _initFormId();
    // Setup listener untuk perubahan form
    _setupFormChangeListeners();

    // api.getComboCategory(controllers: this);
    api.getComboCategoryById(controllers: this, key: 'AgentLevel');
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    parseDataAgentLevel(response);
  }

  void parseDataAgentLevel(response) {
    agentLevelList.clear();
    agentLevelList.assignAll(
      (response as List)
          .map((item) => ComboBoxAgentLevel.fromJSon(item))
          .toList(),
    );

    isAgentLoading.value = false;
  }

  // Muat data recruiter dari SharedPreferences
  void loadRecruiterDataFromPrefs() {
    if (recruiterName.value.isEmpty) {
      recruiterName.value = prefs.getString(kStorageAgentName) ?? '';
      recruiterId.value = prefs.getString(kStorageUserId) ?? '';
      recruiterCode.value = prefs.getString(kStorageAgentCode) ?? '';
      recruiterBranch.value = prefs.getString(kStorageAgentBranch) ?? '';
      recruiterLevel.value = prefs.getString(kStorageUserLevel) ?? '';
    }
  }

  @override
  void onClose() {
    // Batalkan timer auto-save
    _autoSaveTimer?.cancel();
    // Simpan form terakhir kali sebelum menutup halaman
    saveFormData();
    super.onClose();
  }

  // Inisialisasi form ID
  Future<void> _initFormId() async {
    // Cek apakah ada form ID dari parameter
    if (Get.parameters.containsKey('formId')) {
      formId.value = Get.parameters['formId']!;
      log('Menggunakan form ID dari parameter: ${formId.value}');
      // Load form data jika ada
      _loadFormData();
    } else {
      // Buat form ID baru menggunakan UUID
      formId.value = const Uuid().v4();
      log('Membuat form ID baru: ${formId.value}');
      loadRecruiterDataFromPrefs();
    }
  }

  // Setup listener untuk perubahan form
  void _setupFormChangeListeners() {
    // Tambahkan listener untuk Form Verification - RxString
    recruiterName.listen((_) => _onFormChanged());
    recruiterId.listen((_) => _onFormChanged());
    recruiterBranch.listen((_) => _onFormChanged());
    recruiterCode.listen((_) => _onFormChanged());
    recruiterLevel.listen((_) => _onFormChanged());
    recruiterPhoto.listen((_) => _onFormChanged());
    candidateLevelController.addListener(_onFormChanged);
    candidateBranchController.addListener(_onFormChanged);

    // Tambahkan listener untuk FormIdentification
    nikController.addListener(_onFormChanged);
    namaKtpController.addListener(_onFormChanged);
    tempatLahirController.addListener(_onFormChanged);
    tanggalLahirController.addListener(_onFormChanged);
    bulanLahirController.addListener(_onFormChanged);
    tahunLahirController.addListener(_onFormChanged);
    jenisKelaminController.addListener(_onFormChanged);
    alamatKtpController.addListener(_onFormChanged);
    rtKtpController.addListener(_onFormChanged);
    rwKtpController.addListener(_onFormChanged);
    provinsiKtpController.addListener(_onFormChanged);
    kabupatenKtpController.addListener(_onFormChanged);
    kecamatanKtpController.addListener(_onFormChanged);
    kelurahanKtpController.addListener(_onFormChanged);

    // Tambahkan listener untuk Alamat Domisili
    alamatDomisiliController.addListener(_onFormChanged);
    rtDomisiliController.addListener(_onFormChanged);
    rwDomisiliController.addListener(_onFormChanged);
    provinsiDomisiliController.addListener(_onFormChanged);
    kabupatenDomisiliController.addListener(_onFormChanged);
    kecamatanDomisiliController.addListener(_onFormChanged);
    kelurahanDomisiliController.addListener(_onFormChanged);

    // Tambahkan listener untuk FormSelfIdentification
    emailController.addListener(_onFormChanged);
    nomorHpController.addListener(_onFormChanged);
    emergencyNamaController.addListener(_onFormChanged);
    emergencyHubunganController.addListener(_onFormChanged);
    emergencyNomorHpController.addListener(_onFormChanged);
    namaPemilikRekeningController.addListener(_onFormChanged);
    nomorRekeningController.addListener(_onFormChanged);

    // Tambahkan listener untuk perubahan gambar
    ktpImage.listen((_) => _onFormChanged());
    selfieKtpImage.listen((_) => _onFormChanged());
    pasFotoImage.listen((_) => _onFormChanged());

    // Tambahkan listener untuk perubahan URL gambar
    ktpUrl.listen((_) => _onFormChanged());
    selfieKtpUrl.listen((_) => _onFormChanged());
    pasFotoUrl.listen((_) => _onFormChanged());
  }

  // Handler ketika form berubah
  void _onFormChanged() {
    // Jika timer sudah berjalan, reset
    _autoSaveTimer?.cancel();

    // Mulai timer baru untuk auto-save
    _autoSaveTimer = Timer(Duration(seconds: _autoSaveIntervalSeconds), () {
      // Simpan form data
      saveFormData();
    });
  }

  // Load form data dari Firestore
  Future<void> _loadFormData() async {
    if (formId.value.isEmpty) {
      log('Form ID kosong, tidak dapat memuat data form');
      return;
    }

    setLoading(true);

    try {
      final formData = await _firestoreService.getRecruitmentForm(formId.value);

      if (formData != null) {
        // Isi form dengan data yang ada
        _populateFormWithData(formData);
        isFormLoaded.value = true;
        log('Berhasil memuat data form dengan ID: ${formId.value}');
      } else {
        log('Tidak ada data form dengan ID: ${formId.value}');
      }
    } catch (e) {
      log('Error saat memuat data form: $e');
    } finally {
      setLoading(false);
    }
  }

  // Isi form dengan data dari model
  void _populateFormWithData(RecruitmentFormModel formData) {
    // Isi Form Verification dengan data - RxString
    recruiterName.value = formData.recruiterName ?? '';
    recruiterId.value = formData.recruiterId ?? '';
    recruiterBranch.value = formData.recruiterBranch ?? '';
    recruiterCode.value = formData.recruiterCode ?? '';
    recruiterLevel.value = formData.recruiterLevel ?? '';
    recruiterPhoto.value = formData.recruiterPhoto ?? '';
    candidateLevelController.text = formData.candidateLevel ?? '';
    candidateBranchController.text = formData.candidateBranch ?? '';

    // Isi FormIdentification dengan data
    nikController.text = formData.nik ?? '';
    namaKtpController.text = formData.namaKtp ?? '';
    tempatLahirController.text = formData.tempatLahir ?? '';
    tanggalLahirController.text = formData.tanggalLahir ?? '';
    bulanLahirController.text = formData.bulanLahir ?? '';
    tahunLahirController.text = formData.tahunLahir ?? '';
    jenisKelaminController.text = formData.jenisKelamin ?? '';
    alamatKtpController.text = formData.alamatKtp ?? '';
    rtKtpController.text = formData.rtKtp ?? '';
    rwKtpController.text = formData.rwKtp ?? '';
    provinsiKtpController.text = formData.provinsiKtp ?? '';
    kabupatenKtpController.text = formData.kabupatenKtp ?? '';
    kecamatanKtpController.text = formData.kecamatanKtp ?? '';
    kelurahanKtpController.text = formData.kelurahanKtp ?? '';

    // Isi Alamat Domisili dengan data
    alamatDomisiliController.text = formData.alamatDomisili ?? '';
    rtDomisiliController.text = formData.rtDomisili ?? '';
    rwDomisiliController.text = formData.rwDomisili ?? '';
    provinsiDomisiliController.text = formData.provinsiDomisili ?? '';
    kabupatenDomisiliController.text = formData.kabupatenDomisili ?? '';
    kecamatanDomisiliController.text = formData.kecamatanDomisili ?? '';
    kelurahanDomisiliController.text = formData.kelurahanDomisili ?? '';

    // Isi FormSelfIdentification dengan data
    emailController.text = formData.email ?? '';
    nomorHpController.text = formData.nomorHp ?? '';
    emergencyNamaController.text = formData.emergencyNama ?? '';
    emergencyHubunganController.text = formData.emergencyHubungan ?? '';
    emergencyNomorHpController.text = formData.emergencyNomorHp ?? '';
    namaPemilikRekeningController.text = formData.namaPemilikRekening ?? '';
    nomorRekeningController.text = formData.nomorRekening ?? '';

    // Set status form
    isFormSubmitted.value = formData.isSubmitted ?? false;
    formStatus.value = formData.formStatus ?? 'draft';

    // Load image URLs
    ktpUrl.value = formData.ktpImageUrl ?? '';
    selfieKtpUrl.value = formData.selfieKtpImageUrl ?? '';
    pasFotoUrl.value = formData.pasFotoImageUrl ?? '';

    loadRecruiterDataFromPrefs();

    // TODO: Handle gambar jika perlu
    // Ini memerlukan implementasi tambahan untuk memuat gambar dari path
  }

  // Simpan form data ke Firestore
  Future<bool> saveFormData({bool isSubmit = false}) async {
    if (formId.value.isEmpty) {
      log('Form ID kosong, tidak dapat menyimpan data form');
      return false;
    }

    isFormSaving.value = true;

    try {
      // Buat model dari data form saat ini
      final formData = _createFormModel(isSubmit: isSubmit);

      // Simpan ke Firestore
      final result = await _firestoreService.saveRecruitmentForm(
        formData,
        formId.value,
      );

      if (result) {
        log('Berhasil menyimpan data form dengan ID: ${formId.value}');
        if (isSubmit) {
          isFormSubmitted.value = true;
          formStatus.value = 'submitted';
        }
      } else {
        log('Gagal menyimpan data form dengan ID: ${formId.value}');
      }

      return result;
    } catch (e) {
      log('Error saat menyimpan data form: $e');
      return false;
    } finally {
      isFormSaving.value = false;
    }
  }

  // Buat model dari data form saat ini
  RecruitmentFormModel _createFormModel({bool isSubmit = false}) {
    return RecruitmentFormModel(
      id: formId.value,

      // Data Form Verification - RxString
      recruiterName: recruiterName.value,
      recruiterId: recruiterId.value,
      recruiterBranch: recruiterBranch.value,
      recruiterCode: recruiterCode.value,
      recruiterLevel: recruiterLevel.value,
      recruiterPhoto: recruiterPhoto.value,
      candidateLevel: candidateLevelController.text,
      candidateBranch: candidateBranchController.text,

      // Data FormIdentification
      nik: nikController.text,
      namaKtp: namaKtpController.text,
      tempatLahir: tempatLahirController.text,
      tanggalLahir: tanggalLahirController.text,
      bulanLahir: bulanLahirController.text,
      tahunLahir: tahunLahirController.text,
      jenisKelamin: jenisKelaminController.text,
      alamatKtp: alamatKtpController.text,
      rtKtp: rtKtpController.text,
      rwKtp: rwKtpController.text,
      provinsiKtp: provinsiKtpController.text,
      kabupatenKtp: kabupatenKtpController.text,
      kecamatanKtp: kecamatanKtpController.text,
      kelurahanKtp: kelurahanKtpController.text,

      // Data Alamat Domisili
      alamatDomisili: alamatDomisiliController.text,
      rtDomisili: rtDomisiliController.text,
      rwDomisili: rwDomisiliController.text,
      provinsiDomisili: provinsiDomisiliController.text,
      kabupatenDomisili: kabupatenDomisiliController.text,
      kecamatanDomisili: kecamatanDomisiliController.text,
      kelurahanDomisili: kelurahanDomisiliController.text,

      // Data FormSelfIdentification
      email: emailController.text,
      nomorHp: nomorHpController.text,
      emergencyNama: emergencyNamaController.text,
      emergencyHubungan: emergencyHubunganController.text,
      emergencyNomorHp: emergencyNomorHpController.text,
      namaPemilikRekening: namaPemilikRekeningController.text,
      nomorRekening: nomorRekeningController.text,

      // Data Foto
      ktpImagePath: ktpImage.value?.path,
      selfieKtpImagePath: selfieKtpImage.value?.path,
      pasFotoImagePath: pasFotoImage.value?.path,

      // Data URL Foto
      ktpImageUrl: ktpUrl.value,
      selfieKtpImageUrl: selfieKtpUrl.value,
      pasFotoImageUrl: pasFotoUrl.value,

      // Metadata
      lastUpdated: DateTime.now().millisecondsSinceEpoch,
      isSubmitted: isSubmit ? true : isFormSubmitted.value,
      formStatus: isSubmit ? 'submitted' : formStatus.value,
    );
  }

  // Submit form
  Future<bool> submitForm() async {
    return await saveFormData(isSubmit: true);
  }

  Future<void> pickKtpImage(String title, String type) async {
    final result = await Get.toNamed(
      Routes.PHOTO_PAGE_PANDUAN,
      arguments: {'title': title, 'type': type},
    );
    if (result != null && result is File) {
      try {
        // Rename file
        File renamedFile = await changeFileNameOnly(
          result,
          'foto-${title.replaceAll(' ', '-').toLowerCase()} - ${Utils.getRandomString(length: 6)}.jpg',
        );

        // Simpan file yang sudah di-rename
        if (type == kPhotoTypeKtp) {
          ktpImage.value = renamedFile;
        } else if (type == kPhotoTypeSelfieKtp) {
          selfieKtpImage.value = renamedFile;
        } else if (type == kPhotoTypePasFoto) {
          pasFotoImage.value = renamedFile;
        }

        log("File berhasil di-rename: ${renamedFile.path}");
        // Lakukan OCR terlebih dahulu
        textCognition(renamedFile);

        // Upload image setelah berhasil di-rename
        await uploadImage(renamedFile, type);

        // Trigger auto-save setelah mengubah gambar
        _onFormChanged();
      } catch (e) {
        log("Error saat memproses file KTP: $e");
        // Jika rename gagal, tetap gunakan file asli
        if (title == 'KTP') {
          ktpImage.value = result;
        } else if (title == 'Selfie Dengan KTP') {
          selfieKtpImage.value = result;
        } else if (title == 'Pas Foto') {
          pasFotoImage.value = result;
        }

        // Upload image meskipun rename gagal
        await uploadImage(result, type);

        // Trigger auto-save setelah mengubah gambar
        _onFormChanged();
      }
    }
  }

  Future<File> changeFileNameOnly(File file, String newFileName) async {
    try {
      if (!await file.exists()) {
        throw Exception("File tidak ditemukan: ${file.path}");
      }

      var path = file.path;
      var lastSeparator = path.lastIndexOf(Platform.pathSeparator);
      var newPath = path.substring(0, lastSeparator + 1) + newFileName;

      // Gunakan copy + delete untuk menghindari race condition
      // Copy file ke lokasi baru
      File oldFile = File(newPath);
      if (await oldFile.exists()) {
        print('here');
        await oldFile.delete().then((val) {
          print('here deleted');
        });
      }

      File copiedFile = await file.copy(newPath);

      // Hapus file asli setelah copy berhasil
      await file.delete();

      return copiedFile;
    } catch (e) {
      log("Error saat rename file: $e");
      // Kembalikan file asli jika terjadi error
      return file;
    }
  }

  // Upload image to server using API class
  Future<void> uploadImage(File imageFile, String type) async {
    try {
      // Set upload status
      switch (type) {
        case kPhotoTypeKtp:
          isKtpUploading.value = true;
          ktpUploadProgress.value = 0.0;
          break;
        case kPhotoTypeSelfieKtp:
          isSelfieKtpUploading.value = true;
          selfieKtpUploadProgress.value = 0.0;
          break;
        case kPhotoTypePasFoto:
          isPasFotoUploading.value = true;
          pasFotoUploadProgress.value = 0.0;
          break;
      }

      // Call API upload function
      final result = await api.uploadRecruitmentImage(
        imageFile: imageFile,
        type: type,
        onProgress: (percent) {
          // Update progress
          switch (type) {
            case kPhotoTypeKtp:
              ktpUploadProgress.value = percent;
              break;
            case kPhotoTypeSelfieKtp:
              selfieKtpUploadProgress.value = percent;
              break;
            case kPhotoTypePasFoto:
              pasFotoUploadProgress.value = percent;
              break;
          }
        },
      );
      if (result != null && result['success'] == true) {
        final uploadedUrl = result['url'] as String?;
        if (uploadedUrl != null && uploadedUrl.isNotEmpty) {
          // Store the URL
          switch (type) {
            case kPhotoTypeKtp:
              ktpUrl.value = uploadedUrl;
              break;
            case kPhotoTypeSelfieKtp:
              selfieKtpUrl.value = uploadedUrl;
              break;
            case kPhotoTypePasFoto:
              pasFotoUrl.value = uploadedUrl;
              break;
          }
          log("Upload berhasil untuk $type: $uploadedUrl");
        }
      } else {
        final message = result?['message'] ?? 'Upload gagal';
        log("Upload gagal untuk $type: $message");
      }
    } catch (e) {
      log("Error saat upload gambar $type: $e");
    } finally {
      // Reset upload status
      switch (type) {
        case kPhotoTypeKtp:
          isKtpUploading.value = false;
          ktpUploadProgress.value = 0.0;
          break;
        case kPhotoTypeSelfieKtp:
          isSelfieKtpUploading.value = false;
          selfieKtpUploadProgress.value = 0.0;
          break;
        case kPhotoTypePasFoto:
          isPasFotoUploading.value = false;
          pasFotoUploadProgress.value = 0.0;
          break;
      }
    }
  }

  // Clear image method
  void clearImage(String type) {
    switch (type) {
      case kPhotoTypeKtp:
        ktpImage.value?.delete();
        ktpImage.value = null;
        ktpUrl.value = '';
        break;
      case kPhotoTypeSelfieKtp:
        selfieKtpImage.value?.delete();
        selfieKtpImage.value = null;
        selfieKtpUrl.value = '';
        break;
      case kPhotoTypePasFoto:
        pasFotoImage.value?.delete();
        pasFotoImage.value = null;
        pasFotoUrl.value = '';
        break;
    }
    // Trigger auto-save setelah menghapus gambar
    _onFormChanged();
  }

  textCognition(File file) async {
    // print('here 1');
    final textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);
    final inputImage = InputImage.fromFilePath(file.path);

    final RecognizedText recognizedText = await textRecognizer.processImage(
      inputImage,
    );
    // print('here 2');
    await textRecognizer.close();

    String rawText = recognizedText.text;
    // print("here Hasil OCR:\n$rawText");

    // Contoh parsing dasar (regex bisa disesuaikan):
    final nikRegex = RegExp(r'\b\d{16}\b');
    final namaRegex = RegExp(r'NAMA\s*:\s*(.+)', caseSensitive: false);
    final ttlRegex = RegExp(
      r'TEMPAT/TGL LAHIR\s*:\s*(.+)',
      caseSensitive: false,
    );

    // for (TextBlock block in recognizedText.blocks) {
    //   for (TextLine line in block.lines) {
    //     // print("here Line: ${line.text}");
    //     // Cek apakah line mengandung "NIK", "NAMA", dll
    //   }
    // }

    final nik = nikRegex.firstMatch(rawText)?.group(0);
    final nama = namaRegex.firstMatch(rawText)?.group(1);
    final ttl = ttlRegex.firstMatch(rawText)?.group(1);

    log("here NIK: $nik");
    log("here Nama: $nama");
    log("here TTL: $ttl");

    // KtpData data = await extractKtpDataFromImage(inputImage);
    // print("here Data: ${data.nik}");
    // print("here Data: ${data.nama}");
    // print("here Data: ${data.tempatTglLahir}");
    // print("here Data: ${data.jenisKelamin}");
    // print("here Data: ${data.alamat}");
    // print("here Data: ${data.rtRw}");
    // print("here Data: ${data.kelDesa}");
    // print("here Data: ${data.kecamatan}");
    // print("here Data: ${data.agama}");
    // print("here Data: ${data.statusPerkawinan}");
    // print("here Data: ${data.pekerjaan}");
    // print("here Data: ${data.kewarganegaraan}");
    // print("here Data: ${data.berlakuHingga}");
  }
}

class KtpData {
  final String? nik;
  final String? nama;
  final String? tempatTglLahir;
  final String? jenisKelamin;
  final String? alamat;
  final String? rtRw;
  final String? kelDesa;
  final String? kecamatan;
  final String? agama;
  final String? statusPerkawinan;
  final String? pekerjaan;
  final String? kewarganegaraan;
  final String? berlakuHingga;

  KtpData({
    this.nik,
    this.nama,
    this.tempatTglLahir,
    this.jenisKelamin,
    this.alamat,
    this.rtRw,
    this.kelDesa,
    this.kecamatan,
    this.agama,
    this.statusPerkawinan,
    this.pekerjaan,
    this.kewarganegaraan,
    this.berlakuHingga,
  });
}

Future<KtpData> extractKtpDataFromImage(InputImage image) async {
  final textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);
  final recognizedText = await textRecognizer.processImage(image);
  final text = recognizedText.text;
  final lines = text.split('\n').map((e) => e.trim()).toList();

  String? getField(String label) {
    return lines
        .firstWhere(
          (line) => line.toLowerCase().startsWith(label.toLowerCase()),
          orElse: () => '',
        )
        .replaceAll(RegExp('$label[:\\s]*', caseSensitive: false), '')
        .trim();
  }

  String? getNextLine(String label) {
    final index = lines.indexWhere(
      (line) => line.toLowerCase().contains(label.toLowerCase()),
    );
    return index >= 0 && index + 1 < lines.length ? lines[index + 1] : null;
  }

  final data = KtpData(
    nik: getField("NIK"),
    nama: getField("Nama"),
    tempatTglLahir: getField("Tempat/Tgl Lahir") ?? getNextLine("Tempat"),
    jenisKelamin: getField("Jenis Kelamin"),
    alamat: getField("Alamat") ?? getNextLine("Alamat"),
    rtRw: getField("RT/RW"),
    kelDesa: getField("Kel/Desa") ?? getField("Kelurahan/Desa"),
    kecamatan: getField("Kecamatan"),
    agama: getField("Agama"),
    statusPerkawinan: getField("Status Perkawinan"),
    pekerjaan: getField("Pekerjaan"),
    kewarganegaraan: getField("Kewarganegaraan"),
    berlakuHingga: getField("Berlaku Hingga"),
  );

  await textRecognizer.close();
  return data;
}
