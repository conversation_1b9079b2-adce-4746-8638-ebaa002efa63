import 'dart:io';

import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AttachementBottomController extends BaseControllers {
  String baseUrl = ConfigReader.getBaseUrl();

  RxString ktpUrl = ''.obs;
  RxString kkURl = ''.obs;
  RxString bankUrl = ''.obs;

  Rx<XFile?> ktpFile = Rx<XFile?>(null);
  Rx<XFile?> kkFile = Rx<XFile?>(null);
  Rx<XFile?> bankFile = Rx<XFile?>(null);

  RxDouble indicatorKtp = 0.0.obs;
  RxDouble indicatorKK = 0.0.obs;
  RxDouble indicatorBank = 0.0.obs;

  @override
  void dispose() {
    super.dispose();
    dispose();
  }

  performUpdatePicture({required XFile image, required String type}) async {
    try {
      final form = FormData({
        'file': MultipartFile(File(image.path), filename: image.name),
      });
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString(kStorageToken) ?? '';
      setLoading(true);
      final response = await GetConnect().post(
        '$baseUrl/profile/upload/document',
        form,
        headers: {'Authorization': 'Bearer $token'},
        uploadProgress: (percent) {
          switch (type) {
            case 'ktp':
              indicatorKtp.value = percent;
              break;
            case 'kk':
              indicatorKK.value = percent;
              break;
            case 'bank':
              indicatorBank.value = percent;
              break;
            default:
          }
          // You can update UI instead
        },
      );

      if (response.isOk) {
        setLoading(false);
        switch (type) {
          case 'ktp':
            ktpUrl.value = response.body['initialPreview'][0];
            break;
          case 'kk':
            kkURl.value = response.body['initialPreview'][0];
            break;
          case 'bank':
            bankUrl.value = response.body['initialPreview'][0];
            break;
          default:
        }
        // success
      } else {
        setLoading(false);
        Get.snackbar(
          'Failed',
          '${response.body['error_description'] ?? '-'}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
      }
    } catch (e) {
      // print('here $e');
    }
  }
}
