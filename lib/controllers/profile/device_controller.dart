import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/device_models.dart';
import 'package:pdl_superapp/utils/keys.dart';

class DeviceController extends BaseControllers {
  RxList<DeviceModels> listData = RxList();
  Rx<DeviceModels> activeDevices = DeviceModels().obs;
  @override
  void onInit() {
    super.onInit();
    getDeviceList();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    if (requestCode == kReqRevokeDevices) {
      Get.back();
      getDeviceList();
      return;
    }
    parseDataList(response);
  }

  // @override
  // void loadFailed({required int requestCode, required Response response}) {
  //   super.loadFailed(requestCode: requestCode, response: response);
  //   print('load failed');
  // }

  getDeviceList() async {
    await api.getDeviceList(controllers: this);
  }

  parseDataList(response) async {
    listData.clear();
    if (response != null) {
      for (int i = 0; i < response.length; i++) {
        DeviceModels data = DeviceModels.fromJson(response[i]);
        listData.add(data);
      }
    }
    // get current id
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    String deviceId = '';
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await deviceInfoPlugin.webBrowserInfo;
      deviceId = '${webBrowserInfo.appCodeName}';
    }
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;
      deviceId = androidDeviceInfo.id;
    }
    if (Platform.isIOS) {
      IosDeviceInfo iosDeviceInfo = await deviceInfoPlugin.iosInfo;
      deviceId = '${iosDeviceInfo.identifierForVendor}';
    }
    for (int i = 0; i < listData.length; i++) {
      if (listData[i].deviceId == deviceId) {
        // add current active from list to currentActive
        activeDevices.value = listData[i];
        // remove from list data
        listData.remove(listData[i]);
      }
    }
  }

  performRevokeDevice({required String id}) async {
    await api.performRevokeDevice(
      controllers: this,
      deviceId: Uri.encodeFull(id),
      code: kReqRevokeDevices,
    );
  }
}
