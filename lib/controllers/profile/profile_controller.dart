import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfileController extends BaseControllers {
  Rx<UserModels> userData = UserModels().obs;
  RxBool isBioActive = false.obs;

  final LocalAuthentication auth = LocalAuthentication();
  late SharedPreferences prefs;

  RxString userLevel = ''.obs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    Future.delayed(Duration(seconds: 1)).then((val) async {
      await api.getProfile(controllers: this);
      getBiometricStatus();
    });
  }

  getBiometricStatus() async {
    bool isActive = prefs.getBool(kStorageIsBiometricActive) ?? false;
    isBioActive.value = isActive;
  }

  setBiometric() async {
    final bool canAuthenticateWithBiometrics = await auth.canCheckBiometrics;
    final bool canAuthenticate =
        canAuthenticateWithBiometrics || await auth.isDeviceSupported();

    List bioList = await auth.getAvailableBiometrics();

    if (canAuthenticate && bioList.isNotEmpty) {
      isBioActive.value = !isBioActive.value;
    } else {
      isBioActive.value = false;
      Get.snackbar(
        'title_bio_failed'.tr,
        'subtitle_bio_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorErrorText,
      );
    }
    Utils.setBiometric();
  }

  @override
  void load() async {
    super.load();
    setLoading(true);
    await api.getProfile(controllers: this, debug: true);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    parseData(response);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    setLoading(false);
  }

  parseData(response) async {
    try {
      UserModels data = UserModels.fromJson(response);
      userData.value = data;
      // set local storage
      if (prefs.getString(kStorageUserFirestoreId) == '') {
        await prefs.setString(
          kStorageUserFirestoreId,
          'agents-${data.agentCode}-${data.id}',
        );
      } else {
        await prefs.setString(kStorageUserId, (data.id ?? 0).toString());
        await prefs.setString(kStorageAgentCode, data.agentCode ?? '-');
        await prefs.setString(kStorageAgentName, data.agentName ?? '-');
        await prefs.setString(
          kStorageUserFirestoreId,
          'agents-${data.agentCode}-${data.id}',
        );
      }
      // set currentUserLevel
      await prefs.setString(kStorageUserId, (data.id ?? 0).toString());
      await prefs.setString(kStorageAgentCode, data.agentCode ?? '-');
      await prefs.setString(kStorageAgentName, data.agentName ?? '-');
      await prefs.setString(kStorageAgentBranch, data.branchName ?? '-');
      await prefs.setString(kStorageUserLevel, data.level ?? '-');
      await prefs.setString(kStorageUserLevelComplete, data.roles?.code ?? '-');
      userLevel.value = data.roles?.code ?? '-';
      FirestoreServices().getInitialValue();
    } catch (e) {
      // print('here err $e');
    }
  }

  performLoggedOut() {
    Utils.setLoggedOut();
  }
}
