import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/attachement_bottom_sheet.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/models/request_edit_models.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/common_widgets.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfileEditController extends BaseControllers {
  String baseUrl = ConfigReader.getBaseUrl();
  RxBool isEdit = false.obs;

  final List<String> listBank = ['', 'Panin', 'BCA', 'BNI', 'MANDIRI'];
  final List<String> listStatus = ['', 'Belum Kawin', 'Kawin', 'Cerai'];
  final List<String> listPendidikan = ['', 'SD', 'SMP', 'SMA/SMK', 'S1'];

  UserModels currentUserData = UserModels();
  Rx<UserModels> userData = UserModels().obs;
  RxList<RequestEditModels> requestList = RxList();
  // Profile Picture
  RxString currentProfilePicture = ''.obs;
  // File Attachement
  RxString currentKtpAttachement = ''.obs;
  RxString currentKKAttachement = ''.obs;
  RxString currentBankAttachement = ''.obs;

  RxList<String> inApprovalList = RxList();

  RxString userLevel = ''.obs;

  TextEditingController nameTextController = TextEditingController();
  TextEditingController emailTextController = TextEditingController();
  TextEditingController phoneTextController = TextEditingController();
  TextEditingController agentCodeTextController = TextEditingController();
  TextEditingController branchCodeTextController = TextEditingController();
  TextEditingController levelTextController = TextEditingController();
  TextEditingController bankTextController = TextEditingController();
  TextEditingController bankNumberTextController = TextEditingController();
  TextEditingController addressTextController = TextEditingController();
  TextEditingController maritalTextController = TextEditingController();
  TextEditingController educationTextController = TextEditingController();

  // New text controllers for non-BP/BM/BD roles
  TextEditingController fullNameTextController = TextEditingController();
  TextEditingController usernameTextController = TextEditingController();
  TextEditingController channelTextController = TextEditingController();
  TextEditingController roleNameTextController = TextEditingController();
  TextEditingController branchNameTextController = TextEditingController();
  TextEditingController statusTextController = TextEditingController();

  late SharedPreferences prefs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    load();
  }

  @override
  void load() async {
    super.load();
    userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '-';
    setLoading(true);
    await api.getProfileRequestStatus(
      controllers: this,
      code: kReqGetCurrentUpdateProfile,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqGetCurrentUpdateProfile:
        parseDataRequest(response);
        break;
      case kReqUploadProfile:
        break;
      default:
        parseData(response);
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  parseDataRequest(response) {
    requestList.clear();
    for (int i = 0; i < response['content'].length; i++) {
      RequestEditModels requestData = RequestEditModels.fromJson(
        response['content'][i],
      );
      requestList.add(requestData);
    }

    inApprovalList.clear();
    for (var request in requestList) {
      if ([
        kApprovalStatusNew,
        kApprovalStatusPending,
        kApprovalStatusWaiting,
      ].contains(request.approvalStatus)) {
        for (FieldDataModels detail in request.fieldData) {
          if (!inApprovalList.contains(detail.fieldName)) {
            inApprovalList.add(detail.fieldName ?? '-');
          }
        }
      }
    }
  }

  parseData(response) {
    Get.offAllNamed(Routes.MAIN);
    if (userLevel.value == kUserLevelBp ||
        userLevel.value == kUserLevelBm ||
        userLevel.value == kUserLevelBd) {
      Get.snackbar('Success!', 'Data dalam pengajuan perubahan');
    } else {
      Get.snackbar('Success!', 'Data berhasi di ubah');
    }
  }

  String setSelectedItem(List<String> item, String current) {
    String result = '';

    if (item.contains(current)) {
      result = current;
    }

    return result;
  }

  setInitialData(UserModels userdata) {
    currentUserData = userdata;
    nameTextController.text = userdata.agentName ?? '';
    emailTextController.text = userdata.email ?? '';
    phoneTextController.text = userdata.phoneNumber ?? '';
    agentCodeTextController.text = userdata.agentCode ?? '';
    branchCodeTextController.text = userdata.branchCode ?? '';
    levelTextController.text = userdata.level ?? '';
    bankTextController.text = userdata.bank ?? '';
    bankNumberTextController.text = userdata.bankAccountNumber ?? '';
    addressTextController.text = userdata.address ?? '';
    maritalTextController.text = userdata.maritalStatus ?? '';
    educationTextController.text = userdata.education ?? '';

    // Initialize new text controllers for non-BP/BM/BD roles
    fullNameTextController.text = userdata.name ?? '';
    usernameTextController.text = userdata.username ?? '';
    channelTextController.text = userdata.channel ?? '';
    roleNameTextController.text = userdata.roles?.name ?? '';
    branchNameTextController.text = ''; // No prefill as requested
    statusTextController.text = 'aktif'; // Set to "aktif" as requested

    currentProfilePicture.value = userData.value.picture ?? '';
  }

  checkAttachement(context) {
    // check roles fist, kalau diatas bdm up dia cukup cek profile picture ada apa enggak, terus langsung hit api
    // edit profile cms
    if (userLevel.value != kUserLevelBp &&
        userLevel.value != kUserLevelBm &&
        userLevel.value != kUserLevelBd) {
      performChangeProfile();
      return;
    }

    bool isKtpRequired =
        nameTextController.text.isNotEmpty &&
        currentUserData.agentName != nameTextController.text;
    bool isBankRequired =
        (bankTextController.text.isNotEmpty &&
            currentUserData.agentName != bankTextController.text) ||
        (bankNumberTextController.text.isNotEmpty &&
            currentUserData.agentName != bankNumberTextController.text);
    bool isKKRequired =
        (addressTextController.text.isNotEmpty &&
            currentUserData.agentName != addressTextController.text) ||
        (maritalTextController.text.isNotEmpty &&
            currentUserData.agentName != maritalTextController.text) ||
        (educationTextController.text.isNotEmpty &&
            currentUserData.education != educationTextController.text);
    if (isKtpRequired || isBankRequired || isKKRequired) {
      PdlBottomSheet(
        content: AttachementBottomSheet(
          onFinish: (val) {
            currentKtpAttachement.value = val.ktpUrl ?? '';
            currentKKAttachement.value = val.kkUrl ?? '';
            currentBankAttachement.value = val.bankUrl ?? '';
            performChangeProfile();
          },
          isKtp: isKtpRequired,
          isBank: isBankRequired,
          isKK: isKKRequired,
        ),
        title: 'title_support_document'.tr,
      );
    } else {
      performChangeProfile();
    }
  }

  performChangeProfile() async {
    var data = {};

    // For BP, BM, BD roles
    if (nameTextController.text != '' &&
        currentUserData.agentName != nameTextController.text) {
      data["agentName"] = nameTextController.text;
    }
    if (emailTextController.text != '' &&
        currentUserData.email != emailTextController.text) {
      data["email"] = emailTextController.text;
    }
    if (phoneTextController.text != '' &&
        currentUserData.phoneNumber != phoneTextController.text) {
      data["phoneNumber"] = phoneTextController.text;
    }
    if (bankTextController.text != '' &&
        currentUserData.bank != bankTextController.text) {
      data["bank"] = bankTextController.text;
    }
    if (bankNumberTextController.text != '' &&
        currentUserData.bankAccountNumber != bankNumberTextController.text) {
      data["banikAccountNumber"] = bankNumberTextController.text;
    }
    if (addressTextController.text != '' &&
        currentUserData.address != addressTextController.text) {
      data["address"] = addressTextController.text;
    }
    if (maritalTextController.text != '' &&
        currentUserData.maritalStatus != maritalTextController.text) {
      data["maritalStatus"] = maritalTextController.text;
    }
    if (educationTextController.text != '' &&
        currentUserData.education != educationTextController.text) {
      data["education"] = educationTextController.text;
    }

    // For non-BP/BM/BD roles - all fields are disabled, so no updates needed

    if (currentProfilePicture.value != '') {
      if (userLevel.value != kUserLevelBp &&
          userLevel.value != kUserLevelBm &&
          userLevel.value != kUserLevelBd) {
        data["picture"] = currentProfilePicture.value;
      } else {
        data["photo"] = currentProfilePicture.value;
      }
    }
    // documents attachement
    if (currentBankAttachement.value != '') {
      data["bankAttachment"] = currentBankAttachement.value;
    }
    if (currentKKAttachement.value != '') {
      data["kkAttachment"] = currentKKAttachement.value;
    }
    if (currentKtpAttachement.value != '') {
      data["ktpAttachment"] = currentKtpAttachement.value;
    }
    setLoading(true);

    if (userLevel.value == kUserLevelBp ||
        userLevel.value == kUserLevelBm ||
        userLevel.value == kUserLevelBd) {
      await api.patchProfile(controllers: this, data: data);
      return;
    } else {
      data['name'] = currentUserData.name;
      data['email'] = currentUserData.email ?? '';
      data['phone'] = currentUserData.phone ?? '';
      await api.updateProfileCms(controllers: this, data: data);
      return;
    }
  }

  performUpdateProfilePicture({
    required XFile image,
    required String url,
  }) async {
    try {
      final form = FormData({
        'file': MultipartFile(File(image.path), filename: image.name),
      });
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString(kStorageToken) ?? '';
      setLoading(true);
      final response = await GetConnect().post(
        url,
        form,
        headers: {'Authorization': 'Bearer $token'},
        uploadProgress: (percent) {
          // You can update UI instead
        },
      );

      if (response.isOk) {
        setLoading(false);
        String url = response.body['initialPreview'][0];
        currentProfilePicture.value = url;
        // success
      } else {
        setLoading(false);
        Get.snackbar(
          'Failed',
          '${response.body['error_description'] ?? '-'}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
      }
    } catch (e) {
      // print('here $e');
    }
  }

  profileBottomSheet(context, {XFile? profileImage}) {
    PdlBottomSheet(
      title: 'title_profile_picture'.tr,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingMedium),
          SizedBox(
            width: Get.width,
            child: Text(
              'subtitle_profile_picture'.tr,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          SizedBox(height: paddingMedium),
          Container(
            width: 190,
            height: 190,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(190),
              color: kColorGlobalBgGreen,
            ),
            clipBehavior: Clip.hardEdge,
            child:
                profileImage != null
                    ? Image.file(
                      File(profileImage.path),
                      fit: BoxFit.cover,
                      alignment: Alignment.center,
                    )
                    : Center(
                      child: Obx(
                        () => Text(
                          Utils.getInitials(userData.value.agentName ?? '-'),
                          style: Theme.of(
                            context,
                          ).textTheme.headlineLarge?.copyWith(
                            color: kColorTextTersierLight,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
          ),
          SizedBox(height: paddingMedium),
          if (profileImage != null)
            CommonWidgets.errorCard(
              context,
              content: 'title_profile_warning'.tr,
            ),
          SizedBox(height: paddingMedium),
          SizedBox(
            width: Get.width,
            child:
                profileImage != null
                    ? Row(
                      children: [
                        Expanded(
                          child: FilledButton(
                            onPressed: () {
                              Get.back();
                            },
                            style: ButtonStyle(
                              backgroundColor: WidgetStatePropertyAll(
                                Theme.of(context).colorScheme.surface,
                              ),
                              foregroundColor: WidgetStatePropertyAll(
                                Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            child: Text('label_cancel'.tr),
                          ),
                        ),
                        SizedBox(width: paddingMedium),
                        Expanded(
                          child: PdlButton(
                            controller: this,
                            onPressed: () {
                              setLoading(true);
                              performUpdateProfilePicture(
                                image: profileImage,
                                url: '$baseUrl/profile/profile-picture',
                              );
                              Get.back();
                            },
                            title: 'button_save'.tr,
                          ),
                        ),
                      ],
                    )
                    : FilledButton(
                      onPressed: () {
                        Get.back();
                        profileSelectBottom(context);
                      },
                      child: Text('Ubah Foto'),
                    ),
          ),
        ],
      ),
    );
  }

  profileSelectBottom(context) {
    PdlBottomSheet(
      title: 'title_change_profile'.tr,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingMedium),
          CommonWidgets.bottomSheetOptionCard(
            context,
            title: 'Pilih dari Galery',
            prefix: Utils.cachedSvgWrapper(
              'icon/ic-linear-image.svg',
              width: 24,
            ),
            onTap:
                () => Utils.imagePicker(
                  context,
                  onSuccess: (val) {
                    Get.back();
                    profileBottomSheet(context, profileImage: val);
                  },
                ),
          ),
          Divider(),

          CommonWidgets.bottomSheetOptionCard(
            context,
            title: 'Ambil Foto',
            prefix: Utils.cachedSvgWrapper(
              'icon/ic-linear-camera-minimalistic.svg',
              width: 24,
            ),
            onTap:
                () => Utils.imagePicker(
                  context,
                  isCamera: true,
                  onSuccess: (val) {
                    Get.back();
                    profileBottomSheet(context, profileImage: val);
                  },
                ),
          ),
        ],
      ),
    );
  }
}
