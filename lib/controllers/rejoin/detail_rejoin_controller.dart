import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/utils.dart';

class DetailRejoinController extends BaseControllers {
  Rx<File?> ktpImage = Rx<File?>(null);
  final maxKtpSize = 2048;
  final selectedKtpSize = Rxn<double>();

  bool isNewRequest = true;

  @override
  void onInit() {
    super.onInit();
    isNewRequest = Get.arguments['is_new'];
    log('is new ? ${Get.arguments['is_new']}');
  }

  Future<void> pickKtpImage(BuildContext context) async {
    Utils.imagePicker(
      context,
      onSuccess: (val) async {
        final croppedImage = await Utils.cropKtpImage(
          context,
          imagePath: val.path,
        );
        if (croppedImage != null) {
          final selectedFile = File(croppedImage.path);
          final fileSize = await selectedFile.length();
          final sizeInMb = (fileSize / 1024 / 1024);
          if (sizeInMb <= (maxKtpSize / 1024)) {
            ktpImage.value = selectedFile;
            selectedKtpSize.value = sizeInMb;
          } else {
            Get.snackbar(
              'Error',
              'File yang anda pilih berukuran ${sizeInMb.toStringAsFixed(2)}MB. Ukuran file tidak boleh lebih dari ${(maxKtpSize / 1024).toStringAsFixed(0)}MB',
              snackPosition: SnackPosition.BOTTOM,
            );
          }
        }
      },
    );
  }

  void deleteSelectedKtp() {
    selectedKtpSize.value = null;
    ktpImage.value = null;
  }
}
