import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/birthday_customer_model.dart';
import 'package:pdl_superapp/models/birthday_template_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class BirthdayController extends BaseControllers {
  late SharedPreferences prefs;

  // Store the birthday data
  RxList<BirthdayCustomerModel> birthdayList = <BirthdayCustomerModel>[].obs;

  // Store the birthday template
  Rx<BirthdayTemplateModel?> birthdayTemplate = Rx<BirthdayTemplateModel?>(
    null,
  );

  // Error handling
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    await fetchBirthdayData();
    await fetchBirthdayTemplate();
  }

  // Fetch birthday data from API
  Future<void> fetchBirthdayData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';

    try {
      final agentCode = prefs.getString(kStorageAgentCode);

      if (agentCode != null) {
        // Get current date for default date range
        final now = DateTime.now();
        final startDate = DateFormat('yyyy-MM-dd').format(now);
        final endDate = DateFormat(
          'yyyy-MM-dd',
        ).format(now.add(const Duration(days: 30)));

        await api.getBirthdayCustomer(
          controllers: this,
          params: "agentCode=$agentCode&startDate=$startDate&endDate=$endDate",
        );
      } else {
        hasError.value = true;
        errorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == 0) {
      parseBirthdayData(response);
    } else if (requestCode == 1) {
      parseBirthdayTemplate(response);
    }

    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasError.value = true;
    errorMessage.value = 'Failed to load birthday data';
    setLoading(false);
  }

  // Parse the birthday response data
  void parseBirthdayData(dynamic response) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <BirthdayCustomerModel>[];

      if (response is List) {
        for (var item in response) {
          tempList.add(BirthdayCustomerModel.fromJson(item));
        }
      }

      // Update the observable list all at once
      birthdayList.assignAll(tempList);
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  // Fetch birthday template
  Future<void> fetchBirthdayTemplate() async {
    try {
      await api.getBirthdayTemplate(controllers: this, code: 1);
    } catch (e) {
      // print('Error fetching birthday template: $e');
    }
  }

  // Parse birthday template
  void parseBirthdayTemplate(dynamic response) {
    try {
      if (response != null) {
        birthdayTemplate.value = BirthdayTemplateModel.fromJson(response);
      }
    } catch (e) {
      // print('Error parsing birthday template: $e');
    }
  }

  // Send birthday greeting via WhatsApp
  Future<void> sendBirthdayGreeting(String phoneNumber, String name) async {
    try {
      // Get the template
      String template =
          birthdayTemplate.value?.value ?? 'Selamat ulang tahun {name}!';

      // Replace the placeholder with the customer name
      String message = template.replaceAll('{name}', name);

      // Format the phone number (remove any non-digit characters)
      String formattedPhone = phoneNumber.replaceAll(RegExp(r'\D'), '');

      // If the phone number doesn't start with '+', add the country code
      if (!formattedPhone.startsWith('+')) {
        // Assuming Indonesia (+62) as the default country code
        if (formattedPhone.startsWith('0')) {
          formattedPhone = formattedPhone.substring(1);
        } else {
          formattedPhone = formattedPhone;
        }
      }

      // Encode the message for URL
      String encodedMessage = Uri.encodeComponent(message);

      // Create the WhatsApp URL
      final Uri whatsappUrl = Uri.parse(
        'https://wa.me/$formattedPhone?text=$encodedMessage',
      );

      // Launch WhatsApp
      if (await canLaunchUrl(whatsappUrl)) {
        await launchUrl(whatsappUrl, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch WhatsApp';
      }
    } catch (e) {
      // print('Error sending birthday greeting: $e');
    }
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd MMM yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  // Get initial from relation
  String getInitial(String relation) {
    if (relation.isEmpty) return '';

    switch (relation.toUpperCase()) {
      case 'PEMEGANG POLIS':
        return 'PP';
      case 'TERTANGGUNG':
        return 'TT';
      case 'SPOUSE':
        return 'SP';
      case 'CHILD':
        return 'CH';
      case 'PARENT':
        return 'PR';
      default:
        // Get first letter of each word
        final words = relation.split(' ');
        if (words.length > 1) {
          return '${words[0][0]}${words[1][0]}';
        } else if (relation.length > 1) {
          return relation.substring(0, 2);
        } else {
          return relation;
        }
    }
  }
}
