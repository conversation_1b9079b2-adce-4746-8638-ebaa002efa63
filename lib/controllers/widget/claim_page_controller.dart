import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/claim_tracking_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ClaimPageController extends BaseControllers {
  late SharedPreferences prefs;

  // Store the claim tracking data
  RxList<ClaimTrackingModel> claimTrackingList = <ClaimTrackingModel>[].obs;

  // Filtered list for UI
  RxList<ClaimTrackingModel> filteredList = <ClaimTrackingModel>[].obs;

  // Search and filter state
  RxString searchQuery = ''.obs;
  RxMap<String, dynamic> activeFilters = <String, dynamic>{}.obs;

  // Error handling
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  // Parameters
  final String? agentCode;

  ClaimPageController({this.agentCode});

  @override
  void onInit() {
    super.onInit();
    fetchClaimTrackingData();

    // Set up listeners for search and filter changes
    ever(searchQuery, (_) => applyFilters());
    ever(activeFilters, (_) => applyFilters());
  }

  // Fetch claim tracking data from API
  Future<void> fetchClaimTrackingData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';

    try {
      prefs = await SharedPreferences.getInstance();
      final String codeToUse = agentCode ?? prefs.getString(kStorageAgentCode) ?? '';

      if (codeToUse.isNotEmpty) {
        await api.getStatusClaim(controllers: this, params: "agent_code=$codeToUse");
      } else {
        hasError.value = true;
        errorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({required int requestCode, required response, required int statusCode}) {
    super.loadSuccess(requestCode: requestCode, response: response, statusCode: statusCode);

    if (requestCode == 0) {
      parseClaimTrackingData(response);
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasError.value = true;
    errorMessage.value = 'Failed to load claim tracking data';
    setLoading(false);
  }

  // Parse the claim tracking response data
  void parseClaimTrackingData(dynamic response) {
    try {
      claimTrackingList.clear();

      if (response is List) {
        for (var item in response) {
          claimTrackingList.add(ClaimTrackingModel.fromJson(item));
        }
      }

      // Apply filters to the updated list
      applyFilters();

      setLoading(false);
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  // Apply filters to the claim list
  void applyFilters() {
    filteredList.value =
        claimTrackingList.where((claim) {
          // Apply search filter
          bool matchesSearch = true;
          if (searchQuery.isNotEmpty) {
            matchesSearch =
                claim.claimId.toLowerCase().contains(searchQuery.toLowerCase()) ||
                claim.policyHolder.toLowerCase().contains(searchQuery.toLowerCase()) ||
                claim.policyNumber.toLowerCase().contains(searchQuery.toLowerCase());
          }

          // Apply status filter
          bool matchesStatus = true;
          if (activeFilters.containsKey('status') && (activeFilters['status'] as List).isNotEmpty) {
            matchesStatus = (activeFilters['status'] as List).contains(claim.claimStatus);
          }

          // Apply view filter (you may need to adjust this based on your data structure)
          bool matchesView = true;
          if (activeFilters.containsKey('view') && activeFilters['view'] != null) {
            // Example implementation - adjust based on your actual data
            // matchesView = claim.viewType == activeFilters['view'];
          }

          return matchesSearch && matchesStatus && matchesView;
        }).toList();
  }

  // Reset all filters
  void resetFilters() {
    searchQuery.value = '';
    activeFilters.clear();
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  // Format currency
  String formatCurrency(double amount, String currency) {
    final formatter = NumberFormat.currency(symbol: currency, decimalDigits: 2);
    return formatter.format(amount);
  }

  // Refresh data
  void refreshData() {
    fetchClaimTrackingData();
  }
}
