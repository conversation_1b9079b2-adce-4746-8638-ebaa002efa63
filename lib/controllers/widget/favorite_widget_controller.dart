import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/home_controller.dart';
import 'package:pdl_superapp/models/draggable_widget_model.dart';
import 'package:pdl_superapp/models/widget_item_model.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FavoriteWidgetController extends BaseControllers {
  final HomeController homeController;

  FavoriteWidgetController({required this.homeController});
  late SharedPreferences prefs;

  // Lists to store favorite and available widgets
  final RxList<WidgetItemModel> favoriteWidgets = <WidgetItemModel>[].obs;
  final RxList<WidgetItemModel> availableWidgets = <WidgetItemModel>[].obs;

  // Track original favorites to detect changes
  final RxList<String> originalFavoriteIds = <String>[].obs;

  // Flag to track if changes have been made
  final RxBool hasChanges = false.obs;

  // Storage key for saving favorite widgets
  static const String kStorageFavoriteWidgets = 'favorite_widgets';

  // Maximum number of favorite widgets allowed
  static const int maxFavoriteWidgets = 8;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();

    // Initialize data
    await loadWidgets();
  }

  // Load widgets from Firestore first, then fallback to preferences
  Future<void> loadWidgets() async {
    setLoading(true);

    try {
      // Create the full list of all available widgets
      List<WidgetItemModel> allWidgets = [
        WidgetItemModel(
          id: '1',
          label: 'Ulang Tahun Nasabah',
          iconUrl: 'icon/ic-menu-ultah-nasabah.svg',
          color: Colors.blue,
          widgetKey: kWidgetKeyUlangTahunNasabah,
        ),
        WidgetItemModel(
          id: '2',
          label: 'Status Klaim',
          iconUrl: 'icon/ic-menu-status-klaim.svg',
          color: Colors.green,
          widgetKey: kWidgetKeyStatusKlaim,
        ),
        WidgetItemModel(
          id: '3',
          label: 'Polis Lapse',
          iconUrl: 'icon/ic-menu-polis-lapse.svg',
          color: Colors.orange,
          widgetKey: kWidgetKeyPolisLapse,
        ),
        WidgetItemModel(
          id: '4',
          label: 'Polis Jatuh Tempo',
          iconUrl: 'icon/ic-menu-polis jatuh-tempo.svg',
          color: Colors.red,
          widgetKey: kWidgetKeyPolisJatuhTempo,
        ),
        WidgetItemModel(
          id: '5',
          label: 'Produksi Saya',
          iconUrl: 'icon/ic-menu-produksi-saya.svg',
          color: Colors.purple,
          widgetKey: kWidgetKeyProduksiSaya,
        ),
        WidgetItemModel(
          id: '6',
          label: 'Validasi & Promosi',
          iconUrl: 'icon/ic-menu-validasi.svg',
          color: Colors.teal,
          widgetKey: kWidgetKeyValidasiPromosi,
        ),
        WidgetItemModel(
          id: '7',
          label: 'Estimasi Kompensasi',
          iconUrl: 'icon/ic-menu-estimasi.svg',
          color: Colors.amber,
          widgetKey: kWidgetKeyEstimasiKompensasi,
        ),
        WidgetItemModel(
          id: '8',
          label: 'Persistensi',
          iconUrl: 'icon/ic-menu-persistensi.svg',
          color: Colors.indigo,
          widgetKey: kWidgetKeyPersistensi,
        ),
        WidgetItemModel(
          id: '9',
          label: 'Status SPAJ',
          iconUrl: 'icon/ic-menu-spaj.svg',
          color: Colors.cyan,
          widgetKey: kWidgetKeyStatusSpaj,
        ),
      ];

      // Try to get favorite widget IDs from Firestore first
      List<String> favoriteIds = [];
      try {
        // Get FirestoreServices instance
        FirestoreServices firestoreServices;
        try {
          firestoreServices = Get.find<FirestoreServices>();
        } catch (e) {
          log('FirestoreServices not found, creating new instance');
          firestoreServices = Get.put(FirestoreServices());
        }

        // Get favorite widgets from Firestore
        List<String>? firestoreIds =
            await firestoreServices.getFavoriteWidgets();

        // If Firestore data is available, use it
        if (firestoreIds != null && firestoreIds.isNotEmpty) {
          log('Using favorite widgets from Firestore');
          favoriteIds = firestoreIds;

          // Also update SharedPreferences to keep them in sync
          await prefs.setStringList(kStorageFavoriteWidgets, favoriteIds);
        } else {
          // Fallback to SharedPreferences if Firestore data is not available
          log('Falling back to SharedPreferences for favorite widgets');
          favoriteIds = prefs.getStringList(kStorageFavoriteWidgets) ?? [];
        }
      } catch (e) {
        log('Error loading from Firestore, using SharedPreferences: $e');
        // Fallback to SharedPreferences if there's an error with Firestore
        favoriteIds = prefs.getStringList(kStorageFavoriteWidgets) ?? [];
      }

      // Store original favorites for comparison
      originalFavoriteIds.clear();
      originalFavoriteIds.addAll(favoriteIds);

      // Populate favorite widgets list
      favoriteWidgets.clear();
      if (favoriteIds.isNotEmpty) {
        for (String id in favoriteIds) {
          final widget = allWidgets.firstWhere(
            (widget) => widget.id == id,
            orElse: () => allWidgets[0], // Default to first widget if not found
          );
          favoriteWidgets.add(widget);
        }
      } else {
        // Default favorites if none saved (first 4 widgets)
        favoriteWidgets.addAll(allWidgets.take(4));
        // Update original favorites with defaults
        originalFavoriteIds.addAll(allWidgets.take(4).map((w) => w.id));
      }

      // Populate available widgets list (all widgets)
      availableWidgets.clear();
      availableWidgets.addAll(allWidgets);

      // Reset changes flag
      hasChanges.value = false;
    } catch (e) {
      Get.snackbar('Error', 'Failed to load widgets: $e');
    } finally {
      setLoading(false);
    }
  }

  // Add a widget to favorites
  void addToFavorites(WidgetItemModel widget) {
    // Check if widget is already in favorites
    if (favoriteWidgets.any((item) => item.id == widget.id)) {
      return; // Widget already in favorites, do nothing
    }

    if (favoriteWidgets.length < maxFavoriteWidgets) {
      // Add to favorites
      favoriteWidgets.add(widget);

      // Set changes flag
      _checkForChanges();
    } else {
      Get.snackbar(
        'Maximum Reached',
        'You can only have $maxFavoriteWidgets favorite widgets',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Remove a widget from favorites
  void removeFromFavorites(WidgetItemModel widget) {
    // Remove from favorites
    favoriteWidgets.removeWhere((item) => item.id == widget.id);

    // Set changes flag
    _checkForChanges();
  }

  // Handle widget dropped on favorites area
  void onFavoriteDrop(DraggableWidget draggableWidget) {
    final widget = draggableWidget.widget;

    // If the widget is already in favorites and is being reordered
    if (draggableWidget.sourceType == SourceType.favorite) {
      // Handle reordering within favorites
      int oldIndex = draggableWidget.sourceIndex;
      int newIndex = favoriteWidgets.length; // Default to end of list

      // Remove from old position
      favoriteWidgets.removeAt(oldIndex);

      // Insert at new position (end of list for now)
      // The actual position will be determined by the DragTarget
      if (newIndex > oldIndex) {
        newIndex--; // Adjust for removal
      }
      favoriteWidgets.insert(newIndex, widget);
    } else {
      // Widget is coming from available widgets
      addToFavorites(widget);
    }

    // Set changes flag
    _checkForChanges();
  }

  // Handle widget dropped on available area
  void onAvailableDrop(DraggableWidget draggableWidget) {
    // Only remove if it's coming from favorites
    if (draggableWidget.sourceType == SourceType.favorite) {
      removeFromFavorites(draggableWidget.widget);
    }
  }

  // Handle widget dropped at specific position in favorites
  void onFavoriteDropAtPosition(
    DraggableWidget draggableWidget,
    int dropIndex,
  ) {
    final widget = draggableWidget.widget;

    // If the widget is from favorites (reordering)
    if (draggableWidget.sourceType == SourceType.favorite) {
      int oldIndex = draggableWidget.sourceIndex;

      // Skip if dropping at same position or adjacent position
      if (oldIndex == dropIndex || oldIndex == dropIndex - 1) {
        return;
      }

      // Remove from old position
      favoriteWidgets.removeAt(oldIndex);

      // Adjust insert index if needed
      int newIndex = dropIndex;
      if (oldIndex < dropIndex) {
        newIndex--; // Adjust for removal
      }

      // Insert at new position
      favoriteWidgets.insert(newIndex, widget);
    } else {
      // Widget is coming from available widgets

      // Check if widget is already in favorites
      if (favoriteWidgets.any((item) => item.id == widget.id)) {
        return; // Widget already in favorites, do nothing
      }

      if (favoriteWidgets.length < maxFavoriteWidgets) {
        // Insert at specific position
        favoriteWidgets.insert(dropIndex, widget);
      } else {
        Get.snackbar(
          'Maximum Reached',
          'You can only have $maxFavoriteWidgets favorite widgets',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }
    }

    // Set changes flag
    _checkForChanges();
  }

  // Save favorite widget IDs to preferences and Firestore
  Future<void> saveFavorites() async {
    try {
      // Get the list of favorite widget IDs
      List<String> favoriteIds =
          favoriteWidgets.map((widget) => widget.id).toList();

      // Save to SharedPreferences (local storage)
      await prefs.setStringList(kStorageFavoriteWidgets, favoriteIds);

      // Update original favorites
      originalFavoriteIds.clear();
      originalFavoriteIds.addAll(favoriteIds);

      // Reset changes flag
      hasChanges.value = false;

      // Save to Firestore if online
      try {
        FirestoreServices firestoreServices;
        try {
          firestoreServices = Get.find<FirestoreServices>();
        } catch (e) {
          log('FirestoreServices not found, creating new instance');
          firestoreServices = Get.put(FirestoreServices());
        }
        await firestoreServices.saveFavoriteWidgets(favoriteIds);
      } catch (e) {
        log('Error saving to Firestore: $e');
        // Continue anyway as we've already saved to SharedPreferences
      }

      homeController.checkForFavoriteWidgetChanges();
      // Navigate back
      Get.back();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Gagal menyimpan pengaturan: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Check if there are changes compared to original
  void _checkForChanges() {
    List<String> currentIds =
        favoriteWidgets.map((widget) => widget.id).toList();

    // Different number of items means there are changes
    if (currentIds.length != originalFavoriteIds.length) {
      hasChanges.value = true;
      return;
    }

    // Check if all original IDs are in current IDs and in the same order
    for (int i = 0; i < originalFavoriteIds.length; i++) {
      if (i >= currentIds.length || originalFavoriteIds[i] != currentIds[i]) {
        hasChanges.value = true;
        return;
      }
    }

    hasChanges.value = false;
  }

  // Refresh data method
  void refreshData() {
    loadWidgets();
  }
}
