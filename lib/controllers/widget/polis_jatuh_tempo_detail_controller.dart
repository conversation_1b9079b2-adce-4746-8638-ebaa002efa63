import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/polis_jatuh_tempo_upcoming_controller.dart';
import 'package:pdl_superapp/controllers/widget/polis_jatuh_tempo_past_controller.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

// Custom upcoming controller without size limitation
class DetailUpcomingController extends PolisJatuhTempoUpcomingController {
  DetailUpcomingController({super.agentCode});

  // For search
  final RxString searchQuery = ''.obs;
  final RxString searchMode =
      'policyHolderName'.obs; // Default search mode: Name

  @override
  Future<void> fetchPolicyOverdueData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';

    try {
      prefs = await SharedPreferences.getInstance();
      final code = agentCode ?? prefs.getString(kStorageAgentCode);

      if (code != null) {
        // Build params
        String params = "agentCode=$code&type=UPCOMING";

        // Add search parameters if query exists
        if (searchQuery.isNotEmpty) {
          params +=
              "&${searchMode.value}=${Uri.encodeComponent(searchQuery.value)}";
        }

        await api.getPolicyOverdue(controllers: this, params: params);
      } else {
        hasError.value = true;
        errorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }
}

// Custom past controller without size limitation
class DetailPastController extends PolisJatuhTempoPastController {
  DetailPastController({super.agentCode});

  // For search
  final RxString searchQuery = ''.obs;
  final RxString searchMode =
      'policyHolderName'.obs; // Default search mode: Name

  @override
  Future<void> fetchPolicyOverdueData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';

    try {
      prefs = await SharedPreferences.getInstance();
      final code = agentCode ?? prefs.getString(kStorageAgentCode);

      if (code != null) {
        // Build params
        String params = "agentCode=$code&type=PAST";

        // Add search parameters if query exists
        if (searchQuery.isNotEmpty) {
          params +=
              "&${searchMode.value}=${Uri.encodeComponent(searchQuery.value)}";
        }

        await api.getPolicyOverdue(controllers: this, params: params);
      } else {
        hasError.value = true;
        errorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }
}

class PolisJatuhTempoDetailController extends BaseControllers {
  // For search
  final RxString searchQuery = ''.obs;
  final RxString searchMode =
      'policyHolderName'.obs; // Default search mode: Name
  late SharedPreferences prefs;
  String level = '';
  final RxInt selectedSection = 0.obs; // 0 for Akan datang, 1 for Sudah Lewat
  bool isShowOtherAgent = false;
  final String? agentCode;

  // Controllers for different tabs - without size limitation
  late DetailUpcomingController upcomingController;
  late DetailPastController pastController;

  PolisJatuhTempoDetailController({
    this.isShowOtherAgent = false,
    this.agentCode,
  }) {
    if (isShowOtherAgent) {
      selectedSection.value = 1;
    }

    // Initialize controllers with custom API parameters
    upcomingController = Get.put(
      DetailUpcomingController(agentCode: agentCode),
      tag: "upcoming-detail-${DateTime.now().millisecondsSinceEpoch}",
    );

    pastController = Get.put(
      DetailPastController(agentCode: agentCode),
      tag: "past-detail-${DateTime.now().millisecondsSinceEpoch}",
    );
  }

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    setLoading(true);
    refreshData();
  }

  // Switch between sections
  void switchToUpcoming() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToPast() {
    selectedSection.value = 1;
    refreshData();
  }

  // Refresh data based on selected tab
  void refreshData() {
    // Always fetch data for both tabs
    upcomingController.fetchPolicyOverdueData();
    pastController.fetchPolicyOverdueData();
    setLoading(false);
  }

  // Set search mode
  void setSearchMode(String mode) {
    searchMode.value = mode;
    upcomingController.searchMode.value = mode;
    pastController.searchMode.value = mode;

    // Re-fetch data if there's a query
    if (searchQuery.isNotEmpty) {
      applySearch();
    }
  }

  // Apply search
  void applySearch() {
    upcomingController.searchQuery.value = searchQuery.value;
    pastController.searchQuery.value = searchQuery.value;
    refreshData();
  }

  // Reset search
  void resetSearch() {
    searchQuery.value = '';
    upcomingController.searchQuery.value = '';
    pastController.searchQuery.value = '';
    refreshData();
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yy').format(date);
    } catch (e) {
      return dateString;
    }
  }
}
