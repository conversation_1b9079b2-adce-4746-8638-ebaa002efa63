import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/policy_overdue_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PolisJatuhTempoUpcomingController extends BaseControllers {
  late SharedPreferences prefs;

  // Store the policy overdue data
  RxList<PolicyOverdueModel> policyOverdueList = <PolicyOverdueModel>[].obs;

  // Error handling
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  // Optional agent code for viewing other agent's data
  final String? agentCode;

  PolisJatuhTempoUpcomingController({this.agentCode});

  @override
  void onInit() {
    super.onInit();
    fetchPolicyOverdueData();
  }

  // Fetch policy overdue data from API
  Future<void> fetchPolicyOverdueData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';

    try {
      prefs = await SharedPreferences.getInstance();
      final code = agentCode ?? prefs.getString(kStorageAgentCode);

      if (code != null) {
        await api.getPolicyOverdue(controllers: this, params: "agentCode=$code&type=UPCOMING&size=3");
      } else {
        hasError.value = true;
        errorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({required int requestCode, required response, required int statusCode}) {
    super.loadSuccess(requestCode: requestCode, response: response, statusCode: statusCode);

    parsePolicyOverdueData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasError.value = true;
    errorMessage.value = 'Failed to load policy overdue data';
    setLoading(false);
  }

  // Parse the policy overdue response data
  void parsePolicyOverdueData(dynamic response) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <PolicyOverdueModel>[];

      if (response is List) {
        for (var item in response) {
          tempList.add(PolicyOverdueModel.fromJson(item));
        }
      }

      // Update the observable list all at once
      policyOverdueList.assignAll(tempList);
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }
}
