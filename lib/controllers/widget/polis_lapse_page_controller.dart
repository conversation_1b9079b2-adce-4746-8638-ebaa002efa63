import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/policy_lapsed_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class PolisLapsePageController extends BaseControllers {
  late SharedPreferences prefs;
  String level = '';
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team/Group
  final String? agentCode;

  // For search and filter
  final RxString searchQuery = ''.obs;
  final RxString searchMode = 'policyHolderName'.obs; // Default search mode: Name
  final Rx<Map<String, dynamic>> activeFilters = Rx<Map<String, dynamic>>({});

  // Store the policy lapsed data
  RxList<PolicyLapsedModel> policyLapsedList = <PolicyLapsedModel>[].obs;

  // Error handling
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  PolisLapsePageController({this.agentCode});

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    // No listeners for search and filter changes - we'll call applyFilters() explicitly

    fetchPolicyLapsedData();
  }

  // Fetch policy lapsed data from API
  Future<void> fetchPolicyLapsedData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';

    try {
      final code = agentCode ?? prefs.getString(kStorageAgentCode);

      if (code != null) {
        final withDownline = selectedSection.value == 1 ? 1 : 0;
        String params = "agentCode=$code&valueStatus=LAPSE&withDownline=$withDownline";

        // Add search parameters if query exists
        if (searchQuery.isNotEmpty) {
          params += "&${searchMode.value}=${Uri.encodeComponent(searchQuery.value)}";
        }

        // Add date range parameters if they exist
        if (activeFilters.value.containsKey('startDate') && activeFilters.value.containsKey('endDate')) {
          final startDate = activeFilters.value['startDate'];
          final endDate = activeFilters.value['endDate'];
          params += "&startDate=$startDate&endDate=$endDate";
        }

        await api.getPolicyLapsed(controllers: this, params: params);
      } else {
        hasError.value = true;
        errorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({required int requestCode, required response, required int statusCode}) {
    super.loadSuccess(requestCode: requestCode, response: response, statusCode: statusCode);

    parsePolicyLapsedData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasError.value = true;
    errorMessage.value = 'Failed to load policy lapsed data';
    setLoading(false);
  }

  // Parse the policy lapsed response data
  void parsePolicyLapsedData(dynamic response) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <PolicyLapsedModel>[];

      if (response is List) {
        for (var item in response) {
          tempList.add(PolicyLapsedModel.fromJson(item));
        }
      }

      // Update the observable list all at once
      policyLapsedList.assignAll(tempList);

      // Don't call applyFilters() here to avoid infinite loop
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    // Preserve search and filter values when switching tabs
    fetchPolicyLapsedData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    // Preserve search and filter values when switching tabs
    fetchPolicyLapsedData();
  }

  // Set search mode
  void setSearchMode(String mode) {
    searchMode.value = mode;
    // Re-fetch data if there's a query
    if (searchQuery.isNotEmpty) {
      fetchPolicyLapsedData();
    }
  }

  // Refresh data
  void refreshData() {
    fetchPolicyLapsedData();
  }

  // Reset all filters
  void resetFilters() {
    searchQuery.value = '';
    activeFilters.value = {};
    // Don't automatically refresh data here to avoid unexpected API calls
    // The UI will call refreshData() when needed
  }

  // Apply filters to the data
  void applyFilters() {
    // Fetch data with filters applied
    fetchPolicyLapsedData();
  }

  // Getter for filtered list - now just returns the API result
  List<PolicyLapsedModel> get filteredList {
    return policyLapsedList;
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  // Format currency
  String formatCurrency(double amount, String currency) {
    final formatter = NumberFormat.currency(symbol: currency, decimalDigits: 2);
    return formatter.format(amount);
  }

  // Get status display text
  String getDisplayStatus(String status) {
    switch (status) {
      case "ENDING":
        return "Akan Jatuh Tempo";
      case "LAPSE":
        return "Lapse";
      case "ACTIVE":
        return "Aktif";
      default:
        return status;
    }
  }
}
