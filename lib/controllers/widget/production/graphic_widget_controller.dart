import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/production/production_widget_controller.dart';
import 'package:pdl_superapp/models/widget/production_graphic_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

class GraphicWidgetController extends BaseControllers {
  // Observable list to hold chart data
  final RxList<FlSpot> chartData = <FlSpot>[].obs;

  // Lists to store API data
  final RxList<ProductionGraphicModel> monthlyGraphicData =
      <ProductionGraphicModel>[].obs;
  final RxList<ProductionGraphicModel> yearlyGraphicData =
      <ProductionGraphicModel>[].obs;

  // Min and max values for Y axis
  final RxDouble minY = 0.0.obs;
  final RxDouble maxY = 0.0.obs;

  // Track current filter type
  final RxString currentFilterType = kSwitchMonthly.obs;

  // Track current team/individu selection
  final RxString currentTeamType = kSwitchProdIndividu.obs;

  // Chart color based on filter type
  final Rx<Color> chartColor = kColorGlobalBlue.obs;

  // List of month names
  final List<String> months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];

  // List of years (will be populated from API data)
  final RxList<int> years = <int>[].obs;

  // Reference to the production controller to access filter values
  ProductionWidgetController? productionController;

  @override
  void onInit() {
    super.onInit();
    // Get the production controller from Get
    productionController = Get.find<ProductionWidgetController>();

    // Listen to changes in the filter type
    if (productionController != null) {
      ever(productionController!.selectedMonth, (value) {
        currentFilterType.value = value.toString();
        updateChartData();
        updateChartColor();
      });

      ever(productionController!.selectedType, (value) {
        currentTeamType.value = value.toString();

        updateChartData();
      });

      // Initialize with current values
      currentFilterType.value = productionController!.selectedMonth.value;
      currentTeamType.value = productionController!.selectedType.value;
    }

    // Fetch data
    fetchData();
  }

  // Fetch data from API
  void fetchData() async {
    setLoading(true);

    // Fetch monthly data
    await api.getProductionGraphicDataMonth(
      controllers: this,
      code: kReqGetProductionGraphicMonth,
    );

    // Fetch yearly data
    await api.getProductionGraphicDataYear(
      controllers: this,
      code: kReqGetProductionGraphicYear,
    );

    setLoading(false);

    // Update chart with fetched data
    updateChartData();
    updateChartColor();
  }

  // Format currency values (in IDR)
  String formatCurrency(double value) {
    if (value >= 1000000000) {
      // Billions
      return '${(value / 1000000000).toStringAsFixed(1)}B';
    } else if (value >= 1000000) {
      // Millions
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      // Thousands
      return '${(value / 1000).toStringAsFixed(1)}K';
    }
    return '${value.toInt()}';
  }

  // Update chart data based on current filter type and team type
  void updateChartData() {
    chartData.clear();

    // Determine which data to use based on filter type
    List<ProductionGraphicModel> sourceData =
        currentFilterType.value == kSwitchMonthly
            ? monthlyGraphicData
            : yearlyGraphicData;

    if (sourceData.isEmpty) {
      // If no data yet, return
      return;
    }

    // Get user level from the production controller
    String userLevel = '';
    if (productionController != null) {
      userLevel = productionController!.userLevel;
    }

    double highestValue = 0;

    // For monthly data, we have 12 months (0-11)
    if (currentFilterType.value == kSwitchMonthly) {
      // Sort data by month
      sourceData.sort((a, b) => (a.month ?? 0).compareTo(b.month ?? 0));

      // Create a map to store data by month (1-12)
      Map<int, double> dataByMonth = {};

      // Process data based on team/individu selection
      for (var item in sourceData) {
        if (item.month != null && item.netApe != null) {
          double value = 0;

          // Get the appropriate value based on selected tab and user level
          value = _getValueBasedOnSelection(item.netApe, userLevel);

          // Store in map (month is 1-based, but we need 0-based for chart)
          dataByMonth[item.month! - 1] = value;

          // Update highest value
          if (value > highestValue) {
            highestValue = value;
          }
        }
      }

      // Create chart data points for all months (0-11)
      for (int i = 0; i < 12; i++) {
        double value = dataByMonth[i] ?? 0;
        chartData.add(FlSpot(i.toDouble(), value));
      }
    }
    // For yearly data, we have the last 5 years
    else {
      // Sort data by year
      sourceData.sort((a, b) => (a.year ?? 0).compareTo(b.year ?? 0));

      // Extract unique years
      years.clear();
      for (var item in sourceData) {
        if (item.year != null && !years.contains(item.year)) {
          years.add(item.year!);
        }
      }

      // Create a map to store data by year
      Map<int, double> dataByYear = {};

      // Process data based on team/individu selection
      for (var item in sourceData) {
        if (item.year != null && item.netApe != null) {
          double value = 0;

          // Get the appropriate value based on selected tab and user level
          value = _getValueBasedOnSelection(item.netApe, userLevel);

          // Store in map
          dataByYear[item.year!] = value;

          // Update highest value
          if (value > highestValue) {
            highestValue = value;
          }
        }
      }

      // Create chart data points for all years
      for (int i = 0; i < years.length; i++) {
        int year = years[i];
        double value = dataByYear[year] ?? 0;
        chartData.add(FlSpot(i.toDouble(), value));
      }
    }

    // Set min and max Y values for better visualization
    minY.value = 0;
    // Make sure we have a non-zero value for maxY to avoid division by zero errors
    maxY.value =
        highestValue > 0
            ? highestValue * 1.2
            : 1000; // Add 20% padding at the top or use default
  }

  // Helper method to get the appropriate value based on selected tab and user level
  double _getValueBasedOnSelection(
    NetApeGraphicModel? netApe,
    String userLevel,
  ) {
    if (netApe == null) return 0;

    // Check if it's a standard user level (BP, BM, BD)
    final isStandardUserLevel =
        userLevel == kUserLevelBm ||
        userLevel == kUserLevelBd ||
        userLevel == kUserLevelBp;

    if (isStandardUserLevel) {
      // For standard user levels
      if (currentTeamType.value == kSwitchProdIndividu) {
        return (netApe.individu ?? 0).toDouble();
      } else {
        // For BD level, use group data instead of team data
        if (userLevel == kUserLevelBd) {
          return (netApe.group ?? 0).toDouble();
        } else {
          return (netApe.team ?? 0).toDouble();
        }
      }
    } else {
      // For other user levels (BDM, CAO, etc.)
      switch (currentTeamType.value) {
        case kSwitchProdGroup:
          return (netApe.group ?? 0).toDouble();
        case kSwitchProdBranch:
          return (netApe.branch ?? 0).toDouble();
        case kSwitchProdArea:
          return (netApe.area ?? 0).toDouble();
        default:
          // Default to individu data if no specific type is selected
          return (netApe.individu ?? 0).toDouble();
      }
    }
  }

  // Update chart color based on filter type
  void updateChartColor() {
    if (currentFilterType.value == kSwitchMonthly) {
      chartColor.value = kColorGlobalBlue;
    } else {
      chartColor.value = kColorGlobalGreen;
    }

    // Notify UI that filter has changed
    update();
  }

  // Handle API response
  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Check if response is an object and has 'content' key, otherwise use response directly
    final data =
        response is Map && response.containsKey('content')
            ? response['content']
            : response;

    // Parse data based on request code
    if (requestCode == kReqGetProductionGraphicMonth) {
      monthlyGraphicData.clear();

      for (var item in data) {
        monthlyGraphicData.add(ProductionGraphicModel.fromJson(item));
      }
    } else if (requestCode == kReqGetProductionGraphicYear) {
      yearlyGraphicData.clear();

      for (var item in data) {
        yearlyGraphicData.add(ProductionGraphicModel.fromJson(item));
      }
    }

    // Update chart data
    updateChartData();
    updateChartColor();
  }

  // For compatibility with existing code
  RxList<FlSpot> get monthlyData => chartData;
}
