import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/widget/widget_production_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Controller for the Production Widget page
///
/// Handles data fetching, filtering, and state management for production data
/// including individual and team/group data in both monthly and yearly views.
class ProductionWidgetController extends BaseControllers {
  // Tab selection state
  final RxString selectedType = kSwitchProdIndividu.obs;
  final RxString selectedMonth = kSwitchMonthly.obs;

  // Individual data
  final RxList<WidgetProductionDetailModels> arrDataMonthly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> arrDataYearly =
      RxList<WidgetProductionDetailModels>();

  // Filtered individual data for search
  final RxList<WidgetProductionDetailModels> filteredDataMonthly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> filteredDataYearly =
      RxList<WidgetProductionDetailModels>();

  // Team data
  final RxList<WidgetProductionDetailModels> arrDataTeamMonthly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> arrDataTeamYearly =
      RxList<WidgetProductionDetailModels>();

  // Filtered team data for search
  final RxList<WidgetProductionDetailModels> filteredDataTeamMonthly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> filteredDataTeamYearly =
      RxList<WidgetProductionDetailModels>();

  // Area tab data for different roles
  final RxList<WidgetProductionDetailModels> abddDataMonthly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> abddDataYearly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> bdmDataMonthly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> bdmDataYearly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> bddDataMonthly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> bddDataYearly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> hosDataMonthly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> hosDataYearly =
      RxList<WidgetProductionDetailModels>();

  // User information
  late SharedPreferences prefs;
  String userLevel = '';

  // Search state
  final RxString searchText = ''.obs;

  // Minimum search length
  static const int minSearchLength = 3;

  @override
  void onInit() async {
    super.onInit();
    await _initUserLevel();

    // Set up listeners first, before any data loading or tab changes
    _setupListeners();

    // Check for team type argument first
    final teamType = Get.arguments[kArgsProductionTeamType] ?? '';
    if (teamType.isNotEmpty) {
      // Set the selected type from arguments
      printInfo(info: 'Setting selected type from arguments: $teamType');
      selectedType.value = teamType;
    } else {
      // If no argument, set default tab for user level
      _setDefaultTabForUserLevel();
    }

    // Now load data based on the selected tab
    await getData();
  }

  /// Set default tab based on user level
  void _setDefaultTabForUserLevel() {
    final isStandardUserLevel =
        userLevel == kUserLevelBm ||
        userLevel == kUserLevelBd ||
        userLevel == kUserLevelBp;

    if (!isStandardUserLevel) {
      // For other user levels, default to Group tab
      selectedType.value = kSwitchProdGroup;
    }
  }

  /// Initialize user level from shared preferences
  Future<void> _initUserLevel() async {
    try {
      prefs = await SharedPreferences.getInstance();
      userLevel = prefs.getString(kStorageUserLevelComplete) ?? '';
    } catch (e) {
      printError(info: 'Error initializing user level: $e');
      userLevel = '';
    }
  }

  /// Setup reactive listeners for state changes
  void _setupListeners() {
    // Store previous selected type to detect changes
    String previousType = selectedType.value;

    // Listen to changes in the selected type
    ever(selectedType, (newType) {
      // Check if we're switching between different tab types
      bool isTabTypeChanged = _isTabTypeChanged(previousType, newType);

      if (isTabTypeChanged) {
        // Clear team data when switching between different tab types
        _clearTeamData();
        // Force data refresh
        getData();
      } else {
        // Normal refresh check
        refreshDataIfNeeded();
      }

      // Update previous type for next change
      previousType = newType;

      update(); // Trigger UI update for NET APE summary
      _resetSearch();
    });

    // Listen to changes in the selected month/year
    ever(selectedMonth, (_) {
      update();
      _resetSearch();
    });

    // Listen to search text changes
    ever(searchText, (value) {
      if (value.length >= minSearchLength) {
        performSearch(value);
      } else if (value.isEmpty) {
        resetFilteredData();
      }
    });
  }

  /// Check if we're switching between different tab types
  bool _isTabTypeChanged(String previousType, String newType) {
    // Define standard tabs
    final standardTabs = [kSwitchProdIndividu, kSwitchProdTeam];

    // Check if we're switching between different tab groups
    bool wasStandardTab = standardTabs.contains(previousType);
    bool isStandardTab = standardTabs.contains(newType);

    // If both are in the same group, check if they're different tabs within other tabs
    if (!wasStandardTab && !isStandardTab) {
      return previousType != newType;
    }

    // If we're switching between standard and other tabs
    return wasStandardTab != isStandardTab;
  }

  /// Clear team data lists
  void _clearTeamData() {
    arrDataTeamMonthly.clear();
    arrDataTeamYearly.clear();
    filteredDataTeamMonthly.clear();
    filteredDataTeamYearly.clear();
  }

  /// Reset search when switching tabs or time periods
  void _resetSearch() {
    searchText.value = '';
    resetFilteredData();
  }

  /// Refresh data if switching to a tab and data is empty
  void refreshDataIfNeeded() {
    final isStandardUserLevel =
        userLevel == kUserLevelBm ||
        userLevel == kUserLevelBd ||
        userLevel == kUserLevelBp;

    // Always check if individual data is empty
    if (arrDataMonthly.isEmpty || arrDataYearly.isEmpty) {
      printInfo(info: 'Individual data is empty, fetching data');
      getData();
      return;
    }

    if (isStandardUserLevel) {
      // For standard user levels (BP, BM, BD)
      if (selectedType.value == kSwitchProdTeam &&
          (arrDataTeamMonthly.isEmpty || arrDataTeamYearly.isEmpty)) {
        printInfo(
          info: 'Team data is empty for standard user level, fetching data',
        );
        getData();
      }
    } else {
      // For other user levels, always fetch data when switching tabs
      // This ensures we get the correct data for each tab type
      if (selectedType.value != kSwitchProdIndividu) {
        printInfo(info: 'Fetching data for tab: ${selectedType.value}');

        // Special handling for Area tab to ensure data is loaded
        if (selectedType.value == kSwitchProdArea) {
          // Check if area data is empty based on user role
          bool areaDataEmpty = false;

          // Always need BDM data
          if (bdmDataMonthly.isEmpty || bdmDataYearly.isEmpty) {
            areaDataEmpty = true;
          }

          // ABDD, BDD, HOS, and CAO roles need ABDD data
          if ((userLevel == kUserLevelABDD ||
                  userLevel == kUserLevelBDD ||
                  userLevel == kUserLevelHOS ||
                  userLevel == kUserLevelCAO) &&
              (abddDataMonthly.isEmpty || abddDataYearly.isEmpty)) {
            areaDataEmpty = true;
          }

          // BDD, HOS, and CAO roles need BDD data
          if ((userLevel == kUserLevelBDD ||
                  userLevel == kUserLevelHOS ||
                  userLevel == kUserLevelCAO) &&
              (bddDataMonthly.isEmpty || bddDataYearly.isEmpty)) {
            areaDataEmpty = true;
          }

          // HOS and CAO roles need HOS data
          if ((userLevel == kUserLevelHOS || userLevel == kUserLevelCAO) &&
              (hosDataMonthly.isEmpty || hosDataYearly.isEmpty)) {
            areaDataEmpty = true;
          }

          if (areaDataEmpty) {
            printInfo(info: 'Area data is empty, fetching data');
            getData();
          }
        } else {
          // For other tabs, just fetch the data
          getData();
        }
      }
    }
  }

  /// Fetch production data based on user level
  Future<void> getData() async {
    try {
      // Parameters for API calls
      // Use current year and month for real data
      final year = DateTime.now().year.toString();
      final month = DateTime.now().month.toString();
      final paramsMonthly = 'year=$year&month=$month';
      final paramsYearly = 'year=$year';

      setLoading(true);

      // Fetch data based on user level
      await _fetchDataForUserLevel(paramsMonthly, paramsYearly);
    } catch (e) {
      printError(info: 'Error fetching production data: $e');
      // Handle error without using setError which isn't defined
      setLoading(false);
      update();
    } finally {
      // setLoading(false);
    }
  }

  /// Fetch data based on user level
  Future<void> _fetchDataForUserLevel(
    String paramsMonthly,
    String paramsYearly,
  ) async {
    printInfo(info: 'Fetching data for user level: $userLevel');
    switch (userLevel) {
      case kUserLevelBp:
        await _fetchBpData(paramsMonthly, paramsYearly);
        break;
      case kUserLevelBm:
        await _fetchBmData(paramsMonthly, paramsYearly);
        break;
      case kUserLevelBd:
        await _fetchBdData(paramsMonthly, paramsYearly);
        break;
      default:
        // For other user levels (BDM, CAO, etc.)
        await _fetchOtherUserLevelData(paramsMonthly, paramsYearly);
    }
  }

  /// Fetch data for other user levels (BDM, CAO, etc.)
  Future<void> _fetchOtherUserLevelData(
    String paramsMonthly,
    String paramsYearly,
  ) async {
    // Get individual data
    // Get data based on selected type
    if (selectedType.value == kSwitchProdGroup) {
      await api.getWidgetProductionGroup(
        controllers: this,
        params: paramsMonthly,
        code: kReqWidgetProductionTeamMonth,
      );
      await api.getWidgetProductionGroup(
        controllers: this,
        params: paramsYearly,
        code: kReqWidgetProductionTeamYear,
      );
    } else if (selectedType.value == kSwitchProdBranch) {
      await api.getWidgetProductionBranch(
        controllers: this,
        params: paramsMonthly,
        code: kReqWidgetProductionTeamMonth,
      );
      await api.getWidgetProductionBranch(
        controllers: this,
        params: paramsYearly,
        code: kReqWidgetProductionTeamYear,
      );
    } else if (selectedType.value == kSwitchProdArea) {
      // For Area tab, fetch data based on user role according to the specified rules

      // Define custom request codes for each role
      const kReqAbddMonth = 100;
      const kReqAbddYear = 101;
      const kReqBdmMonth = 102;
      const kReqBdmYear = 103;
      const kReqBddMonth = 104;
      const kReqBddYear = 105;
      const kReqHosMonth = 106;
      const kReqHosYear = 107;

      // 1. BDM -> fetch only BDM data
      // 2. ABDD -> fetch BDM and ABDD data
      // 3. BDD -> fetch BDM, ABDD, and BDD data
      // 4. HOS -> fetch BDM, ABDD, BDD, and HOS data
      // 5. CAO -> fetch all data

      // All roles need BDM data
      await api.getWidgetProductionAreaBdm(
        controllers: this,
        params: paramsMonthly,
        code: kReqBdmMonth,
      );
      await api.getWidgetProductionAreaBdm(
        controllers: this,
        params: paramsYearly,
        code: kReqBdmYear,
      );

      // ABDD, BDD, HOS, and CAO roles need ABDD data
      if (userLevel == kUserLevelABDD ||
          userLevel == kUserLevelBDD ||
          userLevel == kUserLevelHOS ||
          userLevel == kUserLevelCAO) {
        await api.getWidgetProductionAreaAbdd(
          controllers: this,
          params: paramsMonthly,
          code: kReqAbddMonth,
        );
        await api.getWidgetProductionAreaAbdd(
          controllers: this,
          params: paramsYearly,
          code: kReqAbddYear,
        );
      }

      // BDD, HOS, and CAO roles need BDD data
      if (userLevel == kUserLevelBDD ||
          userLevel == kUserLevelHOS ||
          userLevel == kUserLevelCAO) {
        await api.getWidgetProductionAreaBdd(
          controllers: this,
          params: paramsMonthly,
          code: kReqBddMonth,
        );
        await api.getWidgetProductionAreaBdd(
          controllers: this,
          params: paramsYearly,
          code: kReqBddYear,
        );
      }

      // HOS and CAO roles need HOS data
      if (userLevel == kUserLevelHOS || userLevel == kUserLevelCAO) {
        await api.getWidgetProductionAreaHos(
          controllers: this,
          params: paramsMonthly,
          code: kReqHosMonth,
        );
        await api.getWidgetProductionAreaHos(
          controllers: this,
          params: paramsYearly,
          code: kReqHosYear,
        );
      }
    } else {
      // Default to group data if no specific type is selected
      await api.getWidgetProductionGroup(
        controllers: this,
        params: paramsMonthly,
        code: kReqWidgetProductionTeamMonth,
      );
      await api.getWidgetProductionGroup(
        controllers: this,
        params: paramsYearly,
        code: kReqWidgetProductionTeamYear,
      );
    }
  }

  /// Fetch data for BP user level
  Future<void> _fetchBpData(String paramsMonthly, String paramsYearly) async {
    await api.getWidgetProductionDetail(
      controllers: this,
      params: paramsMonthly,
      code: kReqWidgetProductionMonth,
    );
    await api.getWidgetProductionDetail(
      controllers: this,
      params: paramsYearly,
      code: kReqWidgetProductionYear,
    );
  }

  /// Fetch data for BM user level
  Future<void> _fetchBmData(String paramsMonthly, String paramsYearly) async {
    // Get individual data
    await api.getWidgetProductionDetail(
      controllers: this,
      params: paramsMonthly,
      code: kReqWidgetProductionMonth,
    );
    await api.getWidgetProductionDetail(
      controllers: this,
      params: paramsYearly,
      code: kReqWidgetProductionYear,
    );

    // Get team data
    await api.getWidgetProductionTeam(
      controllers: this,
      params: paramsMonthly,
      code: kReqWidgetProductionTeamMonth,
    );
    await api.getWidgetProductionTeam(
      controllers: this,
      params: paramsYearly,
      code: kReqWidgetProductionTeamYear,
    );
  }

  /// Fetch data for BD user level
  Future<void> _fetchBdData(String paramsMonthly, String paramsYearly) async {
    // Get individual data
    await api.getWidgetProductionDetail(
      controllers: this,
      params: paramsMonthly,
      code: kReqWidgetProductionMonth,
    );
    await api.getWidgetProductionDetail(
      controllers: this,
      params: paramsYearly,
      code: kReqWidgetProductionYear,
    );

    // Get group data instead of team data
    await api.getWidgetProductionGroup(
      controllers: this,
      params: paramsMonthly,
      code: kReqWidgetProductionTeamMonth,
    );
    await api.getWidgetProductionGroup(
      controllers: this,
      params: paramsYearly,
      code: kReqWidgetProductionTeamYear,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    parseData(response, requestCode: requestCode);
  }

  /// Parse API response data
  void parseData(dynamic response, {required int requestCode}) {
    try {
      // Clear the appropriate list based on request code
      _clearDataListForRequestCode(requestCode);

      // Check if response is an object and has 'content' key, otherwise use response directly
      final data = _extractDataFromResponse(response);

      if (data == null || data.isEmpty) {
        printInfo(info: 'No data found for request code: $requestCode');
        resetFilteredData();
        return;
      }

      // Parse data and add to appropriate list
      _addDataToAppropriateList(data, requestCode);

      // Initialize filtered data with all data
      resetFilteredData();
    } catch (e) {
      printError(info: 'Error parsing data: $e');
    }
  }

  /// Clear the appropriate data list based on request code
  void _clearDataListForRequestCode(int requestCode) {
    // Define custom request codes for each role (must match those in _fetchOtherUserLevelData)
    const kReqAbddMonth = 100;
    const kReqAbddYear = 101;
    const kReqBdmMonth = 102;
    const kReqBdmYear = 103;
    const kReqBddMonth = 104;
    const kReqBddYear = 105;
    const kReqHosMonth = 106;
    const kReqHosYear = 107;

    if (requestCode == kReqWidgetProductionMonth) {
      arrDataMonthly.clear();
    } else if (requestCode == kReqWidgetProductionYear) {
      arrDataYearly.clear();
    } else if (requestCode == kReqWidgetProductionTeamMonth) {
      arrDataTeamMonthly.clear();
    } else if (requestCode == kReqWidgetProductionTeamYear) {
      arrDataTeamYearly.clear();
    }
    // Clear role-specific data lists
    else if (requestCode == kReqAbddMonth) {
      abddDataMonthly.clear();
    } else if (requestCode == kReqAbddYear) {
      abddDataYearly.clear();
    } else if (requestCode == kReqBdmMonth) {
      bdmDataMonthly.clear();
    } else if (requestCode == kReqBdmYear) {
      bdmDataYearly.clear();
    } else if (requestCode == kReqBddMonth) {
      bddDataMonthly.clear();
    } else if (requestCode == kReqBddYear) {
      bddDataYearly.clear();
    } else if (requestCode == kReqHosMonth) {
      hosDataMonthly.clear();
    } else if (requestCode == kReqHosYear) {
      hosDataYearly.clear();
    }
  }

  /// Extract data from response
  dynamic _extractDataFromResponse(dynamic response) {
    return response is Map && response.containsKey('content')
        ? response['content']
        : response;
  }

  /// Add parsed data to the appropriate list
  void _addDataToAppropriateList(dynamic data, int requestCode) {
    // Define custom request codes for each role (must match those in _fetchOtherUserLevelData)
    const kReqAbddMonth = 100;
    const kReqAbddYear = 101;
    const kReqBdmMonth = 102;
    const kReqBdmYear = 103;
    const kReqBddMonth = 104;
    const kReqBddYear = 105;
    const kReqHosMonth = 106;
    const kReqHosYear = 107;

    for (int i = 0; i < data.length; i++) {
      try {
        final item = WidgetProductionDetailModels.fromJson(data[i]);

        if (requestCode == kReqWidgetProductionMonth) {
          arrDataMonthly.add(item);
        } else if (requestCode == kReqWidgetProductionYear) {
          arrDataYearly.add(item);
        } else if (requestCode == kReqWidgetProductionTeamMonth) {
          arrDataTeamMonthly.add(item);
        } else if (requestCode == kReqWidgetProductionTeamYear) {
          arrDataTeamYearly.add(item);
        }
        // Add to role-specific data lists
        else if (requestCode == kReqAbddMonth) {
          abddDataMonthly.add(item);
        } else if (requestCode == kReqAbddYear) {
          abddDataYearly.add(item);
        } else if (requestCode == kReqBdmMonth) {
          bdmDataMonthly.add(item);
        } else if (requestCode == kReqBdmYear) {
          bdmDataYearly.add(item);
        } else if (requestCode == kReqBddMonth) {
          bddDataMonthly.add(item);
        } else if (requestCode == kReqBddYear) {
          bddDataYearly.add(item);
        } else if (requestCode == kReqHosMonth) {
          hosDataMonthly.add(item);
        } else if (requestCode == kReqHosYear) {
          hosDataYearly.add(item);
        }
      } catch (e) {
        printError(info: 'Error parsing item at index $i: $e');
      }
    }
  }

  /// Calculate total net APE for individual monthly data
  num calculateTotalNetApeIndividuMonthly() {
    return _calculateTotalNetApe(arrDataMonthly);
  }

  /// Calculate total net APE for individual yearly data
  num calculateTotalNetApeIndividuYearly() {
    return _calculateTotalNetApe(arrDataYearly);
  }

  /// Calculate total net APE for team monthly data
  num calculateTotalNetApeTeamMonthly() {
    return _calculateTotalNetApe(arrDataTeamMonthly);
  }

  /// Calculate total net APE for team yearly data
  num calculateTotalNetApeTeamYearly() {
    return _calculateTotalNetApe(arrDataTeamYearly);
  }

  /// Helper method to calculate total net APE from a list
  num _calculateTotalNetApe(List<WidgetProductionDetailModels> dataList) {
    num total = 0;
    for (var item in dataList) {
      total += item.netApe ?? 0;
    }
    return total;
  }

  /// Calculate total Net APE for the current user role in the Area tab
  num calculateTotalNetApeForCurrentRole() {
    // Get data based on the current user role
    switch (userLevel) {
      case kUserLevelABDD:
        return _calculateTotalNetApe(getAbddData());
      case kUserLevelBdm:
        return _calculateTotalNetApe(getBdmData());
      case kUserLevelBDD:
        return _calculateTotalNetApe(getBddData());
      case kUserLevelHOS:
        return _calculateTotalNetApe(getHosData());
      default:
        // Default to ABDD data if user role doesn't match any specific role
        return _calculateTotalNetApe(getAbddData());
    }
  }

  /// Get the appropriate total net APE based on current selection
  num getCurrentTotalNetApe() {
    if (selectedType.value == kSwitchProdIndividu) {
      return selectedMonth.value == kSwitchMonthly
          ? calculateTotalNetApeIndividuMonthly()
          : calculateTotalNetApeIndividuYearly();
    } else {
      return selectedMonth.value == kSwitchMonthly
          ? calculateTotalNetApeTeamMonthly()
          : calculateTotalNetApeTeamYearly();
    }
  }

  /// Reset filtered data to show all data
  void resetFilteredData() {
    filteredDataMonthly.clear();
    filteredDataMonthly.addAll(arrDataMonthly);

    filteredDataYearly.clear();
    filteredDataYearly.addAll(arrDataYearly);

    filteredDataTeamMonthly.clear();
    filteredDataTeamMonthly.addAll(arrDataTeamMonthly);

    filteredDataTeamYearly.clear();
    filteredDataTeamYearly.addAll(arrDataTeamYearly);

    update();
  }

  /// Get ABDD data based on selected time period
  List<WidgetProductionDetailModels> getAbddData() {
    return selectedMonth.value == kSwitchMonthly
        ? abddDataMonthly
        : abddDataYearly;
  }

  /// Get BDM data based on selected time period
  List<WidgetProductionDetailModels> getBdmData() {
    return selectedMonth.value == kSwitchMonthly
        ? bdmDataMonthly
        : bdmDataYearly;
  }

  /// Get BDD data based on selected time period
  List<WidgetProductionDetailModels> getBddData() {
    return selectedMonth.value == kSwitchMonthly
        ? bddDataMonthly
        : bddDataYearly;
  }

  /// Get HOS data based on selected time period
  List<WidgetProductionDetailModels> getHosData() {
    return selectedMonth.value == kSwitchMonthly
        ? hosDataMonthly
        : hosDataYearly;
  }

  /// Calculate total net APE for a specific role's data
  num calculateTotalNetApeForRole(List<WidgetProductionDetailModels> roleData) {
    return _calculateTotalNetApe(roleData);
  }

  /// Calculate total net APE for any data list
  num calculateTotalNetApe(List<WidgetProductionDetailModels> dataList) {
    return _calculateTotalNetApe(dataList);
  }

  /// Perform search on the data
  void performSearch(String query) {
    if (query.length < minSearchLength) return;

    final searchQuery = query.toLowerCase();

    // Filter individual monthly data
    filteredDataMonthly.value = _filterIndividualData(
      arrDataMonthly,
      searchQuery,
    );

    // Filter individual yearly data
    filteredDataYearly.value = _filterIndividualData(
      arrDataYearly,
      searchQuery,
    );

    // Filter team monthly data
    filteredDataTeamMonthly.value = _filterTeamData(
      arrDataTeamMonthly,
      searchQuery,
    );

    // Filter team yearly data
    filteredDataTeamYearly.value = _filterTeamData(
      arrDataTeamYearly,
      searchQuery,
    );

    update();
  }

  /// Filter individual data based on search query
  List<WidgetProductionDetailModels> _filterIndividualData(
    List<WidgetProductionDetailModels> dataList,
    String searchQuery,
  ) {
    return dataList
        .where((item) => item.containsSearchQuery(searchQuery))
        .toList();
  }

  /// Filter team data based on search query
  List<WidgetProductionDetailModels> _filterTeamData(
    List<WidgetProductionDetailModels> dataList,
    String searchQuery,
  ) {
    if (selectedType.value == kSwitchProdBranch) {
      return dataList
          .where((item) => item.mainBranchCodeContainsSearchQuery(searchQuery))
          .toList();
    }
    return dataList
        .where((item) => item.agentCodeContainsSearchQuery(searchQuery))
        .toList();
  }

  /// Handle search text change
  void onSearchTextChanged(String value) {
    searchText.value = value;
  }
}
