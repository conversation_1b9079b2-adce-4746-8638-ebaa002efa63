import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/widget/widget_spaj_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SpajController extends BaseControllers {
  late SharedPreferences prefs;

  // Store the SPAJ data
  RxList<WidgetSpajModels> arrData = RxList<WidgetSpajModels>();
  // Store data for individu and team tabs
  RxList<WidgetSpajModels> arrDataIndividu = RxList<WidgetSpajModels>();
  RxList<WidgetSpajModels> arrDataTeam = RxList<WidgetSpajModels>();
  // Store the original data for search functionality
  RxList<WidgetSpajModels> originalData = RxList<WidgetSpajModels>();
  // Search text controller
  final TextEditingController searchController = TextEditingController();

  // Tab selection (0 for Individu, 1 for Team)
  RxInt selectedSection = 0.obs;
  String userLevel = '';

  // Error handling
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;
  String params = '';

  RxList filterValue = RxList();

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    userLevel = prefs.getString(kStorageUserLevel) ?? '';
    load();
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    updateDisplayData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    updateDisplayData();
  }

  // Update the displayed data based on selected tab
  void updateDisplayData() {
    if (selectedSection.value == 0) {
      arrData.assignAll(arrDataIndividu);
      originalData.assignAll(arrDataIndividu);
    } else {
      // If team data is empty but we're switching to team tab, try to load it
      if (arrDataTeam.isEmpty && userLevel != kLevelBP) {
        api.getSpajTeam(controllers: this, params: params, code: 2);
      }
      arrData.assignAll(arrDataTeam);
      originalData.assignAll(arrDataTeam);
    }
  }

  @override
  void onClose() {
    // Dispose the controller to avoid memory leaks
    searchController.dispose();
    super.onClose();
  }

  @override
  void load() {
    super.load();
    setLoading(true);
    searchController.clear();
    api.getSpajIndividu(
      controllers: this,
      params: params,
      code: 1,
    ); // Request code 1 for individu
    if (userLevel != kLevelBP) {
      api.getSpajTeam(
        controllers: this,
        params: params,
        code: 2,
      ); // Request code 2 for team
    }
  }

  void refreshData() {
    load();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Determine if this is an individu or team response based on the request code
    String type = 'individu';

    // If we're handling a team API response
    if (requestCode == 2) {
      type = 'team';
    }

    parseData(response, type: type);
    setLoading(false);
  }

  parseData(response, {String type = 'individu'}) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <WidgetSpajModels>[];

      // Handle different response structures
      var data = [];

      if (response is List) {
        data = response;
      } else if (response is Map) {
        // Try to extract data from different possible response structures
        if (response.containsKey('content') && response['content'] is List) {
          data = response['content'];
        } else if (response.containsKey('data') && response['data'] is List) {
          data = response['data'];
        }
      }

      // Process the data
      for (var item in data) {
        try {
          WidgetSpajModels element = WidgetSpajModels.fromJson(item);
          tempList.add(element);
        } catch (e) {
          // Skip items that can't be parsed
        }
      }

      if (type == 'individu') {
        arrDataIndividu.assignAll(tempList);
        if (selectedSection.value == 0 || userLevel != kLevelBM) {
          arrData.assignAll(tempList);
          originalData.assignAll(tempList);
        }
      } else {
        arrDataTeam.assignAll(tempList);
        if (selectedSection.value == 1 && userLevel != kLevelBP) {
          arrData.assignAll(tempList);
          originalData.assignAll(tempList);
        }
      }

      hasError.value = false;
      errorMessage.value = '';
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = 'Failed to load SPAJ data';
    setLoading(false);
  }

  @override
  void loadError(e, {response}) {
    super.loadError(e, response: response);
    hasError.value = true;
    errorMessage.value = e.toString();
    setLoading(false);
  }

  onTapFilter(String spajStat) {
    if (filterValue.contains(spajStat)) {
      filterValue.remove(spajStat);
    } else {
      filterValue.add(spajStat);
    }
  }

  onTapClearFilter() {
    filterValue.clear();
    searchController.clear();
    params = '';
    refreshData();
    Get.back();
  }

  onTapTerapkan() {
    if (filterValue.isNotEmpty) {
      params =
          'status=${Uri.encodeComponent(filterValue.join('&status='))}&size=10';
      // Reset search text when applying filters
      searchController.clear();
      refreshData();
    } else {
      params = '';
      refreshData();
    }
    Get.back();
  }

  // Search functionality
  void onSearch(String value) {
    if (value.length < 3) {
      // If search text is less than 3 characters, restore original data based on current tab
      updateDisplayData();
      return;
    }

    // Filter data based on search text and current tab
    List<WidgetSpajModels> dataToFilter = originalData;

    // Filter data based on search text
    List<WidgetSpajModels> filteredData =
        dataToFilter.where((item) {
          // Check if policyHolderName contains search text
          bool matchesPolicyHolder =
              item.policyHolderName?.toLowerCase().contains(
                value.toLowerCase(),
              ) ??
              false;
          // Check if spajNumber contains search text
          bool matchesSpajNumber =
              item.spajNumber?.toLowerCase().contains(value.toLowerCase()) ??
              false;

          return matchesPolicyHolder || matchesSpajNumber;
        }).toList();

    // Update the displayed data
    arrData.clear();
    arrData.addAll(filteredData);
  }
}
