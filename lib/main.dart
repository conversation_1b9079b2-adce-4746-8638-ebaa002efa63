import 'dart:async';
import 'dart:developer';

import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/theme_service.dart';

import 'package:device_preview/device_preview.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:pdl_superapp/firebase_options_dev.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/deep_link_parser.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:pdl_superapp/utils/languages/language_controller.dart';
import 'package:pdl_superapp/utils/network_manager.dart';
import 'package:pdl_superapp/widgets/splash_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'app.dart';
import 'utils/import_helper_web/import_helper.dart';

FutureOr<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Show splash screen immediately
  runApp(const SplashScreen());

  try {
    // Add timeout to setupInitializers
    await setupInitializers();
  } catch (e) {
    log(
      'Main initialization failed: $e',
    ); // Using direct log here as LoggerService might not be initialized yet
    // Continue anyway to show at least the basic UI
  }

  final SharedPreferences prefs = await SharedPreferences.getInstance();
  // Replace splash screen with main app
  runApp(
    DevicePreview(
      enabled: false,
      isToolbarVisible: true,
      tools: const [...DevicePreview.defaultTools],
      builder: (context) => App(prefs: prefs),
    ),
  );
}

Future<void> setupInitializers() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Configure URL strategy for web
    if (kIsWeb) {
      useWebFeature();
    }

    // Initialize LoggerService first
    final loggerService =
        await Get.put(LoggerService(), permanent: true).init();
    loggerService.log('LoggerService initialized');

    // Initialize NetworkManager early
    Get.put(NetworkManager(), permanent: true);

    // Initialize date formatting for Indonesian locale
    await initializeDateFormatting('id_ID', null);

    await dotenv.load(fileName: ".env");

    // Initialize Firebase first
    try {
      Get.find<LoggerService>().log('Initializing Firebase...');
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      Get.find<LoggerService>().log('Firebase initialized successfully');

      // Now initialize Firestore for offline access
      try {
        Get.find<LoggerService>().log('Initializing Firestore...');
        final firestoreServices = Get.put(FirestoreServices(), permanent: true);
        await firestoreServices.initFireStore();
        Get.find<LoggerService>().log('Firestore initialized successfully');
      } catch (firestoreError) {
        Get.find<LoggerService>().log(
          'Firestore initialization failed: $firestoreError',
        );
        // Continue without Firestore
      }

      // Try anonymous sign in but don't block app startup if it fails
      try {
        if (FirebaseAuth.instance.currentUser == null) {
          await FirebaseAuth.instance.signInAnonymously();
          Get.find<LoggerService>().log('Anonymous sign-in successful');
        }
      } catch (signInError) {
        Get.find<LoggerService>().log('Anonymous sign-in failed: $signInError');
        // App can still work without authentication for offline access
      }
    } catch (e) {
      Get.find<LoggerService>().log('Firebase initialization failed: $e');
      // App can still work without Firebase for offline access
    }

    // Initialize language controller with error handling
    try {
      LanguageController languageController = Get.put(LanguageController());
      await languageController.getLanguages();
    } catch (e) {
      Get.find<LoggerService>().log('Language initialization failed: $e');
      // Set default language or continue with cached language
    }

    try {
      await ConfigReader.initialize();

      // Initialize ThemeService after ConfigReader is initialized
      try {
        Get.find<LoggerService>().log('Initializing ThemeService...');
        final themeService =
            await Get.put(ThemeService(), permanent: true).init();
        Get.find<LoggerService>().log(
          'ThemeService initialized successfully: ${themeService.currentTheme.name}',
        );
      } catch (themeError) {
        Get.find<LoggerService>().log(
          'ThemeService initialization failed: $themeError',
        );
        // Continue without ThemeService
      }
    } catch (e) {
      Get.find<LoggerService>().log('Config initialization failed: $e');
      // Use default configuration or cached values
    }

    try {
      await DeepLinkParser().initAppLink();
    } catch (e) {
      Get.find<LoggerService>().log('Deep link initialization failed: $e');
    }
  } catch (e) {
    Get.find<LoggerService>().log('Setup initializers failed: $e');
    // Ensure we continue to run the app even if some initializations fail
  }
  Get.find<LoggerService>().log('Setup initializers completed');
}
