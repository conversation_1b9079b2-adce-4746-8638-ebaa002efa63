class BirthdayCustomerModel {
  final String agentCode;
  final String customerName;
  final String birthday;
  final String phoneNumber;
  final String emailAddress;
  final String policyNumber;
  final String relation;

  BirthdayCustomerModel({
    required this.agentCode,
    required this.customerName,
    required this.birthday,
    required this.phoneNumber,
    required this.emailAddress,
    required this.policyNumber,
    required this.relation,
  });

  factory BirthdayCustomerModel.fromJson(Map<String, dynamic> json) {
    return BirthdayCustomerModel(
      agentCode: json['agentCode'] ?? '',
      customerName: json['customerName'] ?? '',
      birthday: json['birthday'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      emailAddress: json['emailAddress'] ?? '',
      policyNumber: json['policyNumber'] ?? '',
      relation: json['relation'] ?? '',
    );
  }
}
