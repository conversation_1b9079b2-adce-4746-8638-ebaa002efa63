class ComboBoxKeyModels {
  String? key;
  String? value;

  ComboBoxKeyModels({this.key, this.value});

  ComboBoxKeyModels.fromJSon(Map json) {
    key = json['key'];
    value = json['value'];
  }
}

class ComboBoxModels {
  String? key;
  String? value;
  String? value2;
  String? value3;

  ComboBoxModels({this.key, this.value, this.value2, this.value3});

  ComboBoxModels.fromJSon(Map json) {
    key = json['key'];
    value = json['value'];
    value2 = json['value2'];
    value3 = json['value3'];
  }
}

class ComboBoxAgentLevel extends ComboBoxModels {
  ComboBoxAgentLevel({
    required super.key,
    required super.value,
    required super.value2,
    required super.value3,
  });

  factory ComboBoxAgentLevel.fromJSon(Map json) {
    return ComboBoxAgentLevel(
      key: json['key'],
      value: json['value'],
      value2: json['value2'],
      value3: json['value3'],
    );
  }
}
