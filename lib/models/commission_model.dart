class CommissionModel {
  final int month;
  final int year;
  final String periode;
  final List<CommissionItemModel> commissions;
  final double totalCommission;

  CommissionModel({
    required this.month,
    required this.year,
    required this.periode,
    required this.commissions,
    required this.totalCommission,
  });

  factory CommissionModel.fromJson(Map<String, dynamic> json) {
    return CommissionModel(
      month: json['month'] ?? 0,
      year: json['year'] ?? 0,
      periode: json['periode'] ?? '',
      commissions: (json['commissions'] as List<dynamic>?)
              ?.map((item) => CommissionItemModel.fromJson(item))
              .toList() ??
          [],
      totalCommission: (json['totalCommission'] ?? 0).toDouble(),
    );
  }
}

class CommissionItemModel {
  final String name;
  final double amount;

  CommissionItemModel({
    required this.name,
    required this.amount,
  });

  factory CommissionItemModel.fromJson(Map<String, dynamic> json) {
    return CommissionItemModel(
      name: json['name'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
    );
  }
}
