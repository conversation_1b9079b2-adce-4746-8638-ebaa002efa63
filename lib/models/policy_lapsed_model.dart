class PolicyLapsedModel {
  final String policyNumber;
  final String policyHolderName;
  final String insuredName;
  final String agentCode;
  final String agentName;
  final String product;
  final String policyStatus;
  final String riskCommencementDate;
  final String paymentFrequency;
  final String dueDate;
  final double basicPremium;
  final double rtu;
  final String currency;
  final String twisting;
  final String nlgStatus;
  final String lastPaidDate;

  PolicyLapsedModel({
    required this.policyNumber,
    required this.policyHolderName,
    required this.insuredName,
    required this.agentCode,
    required this.agentName,
    required this.product,
    required this.policyStatus,
    required this.riskCommencementDate,
    required this.paymentFrequency,
    required this.dueDate,
    required this.basicPremium,
    required this.rtu,
    required this.currency,
    required this.twisting,
    required this.nlgStatus,
    required this.lastPaidDate,
  });

  factory PolicyLapsedModel.fromJson(Map<String, dynamic> json) {
    return PolicyLapsedModel(
      policyNumber: json['policyNumber'] ?? '',
      policyHolderName: json['policyHolderName'] ?? '',
      insuredName: json['insuredName'] ?? '',
      agentCode: json['agentCode'] ?? '',
      agentName: json['agentName'] ?? '',
      product: json['product'] ?? '',
      policyStatus: json['policyStatus'] ?? '',
      riskCommencementDate: json['riskCommencementDate'] ?? '',
      paymentFrequency: json['paymentFrequency'] ?? '',
      dueDate: json['dueDate'] ?? '',
      basicPremium: (json['basicPremium'] ?? 0).toDouble(),
      rtu: (json['rtu'] ?? 0).toDouble(),
      currency: json['currency'] ?? '',
      twisting: json['twisting'] ?? '',
      nlgStatus: json['nlgStatus'] ?? '',
      lastPaidDate: json['lastPaidDate'] ?? '',
    );
  }
}
