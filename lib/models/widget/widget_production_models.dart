/// Model for production summary data
class WidgetProductionSumModels {
  final int? month;
  final int? year;
  final WidgetProductionSumNetApeModels? netApe;

  WidgetProductionSumModels({this.month, this.year, this.netApe});

  /// Create a model from JSON map
  factory WidgetProductionSumModels.fromJson(Map<String, dynamic> json) {
    return WidgetProductionSumModels(
      month: json['month'] as int?,
      year: json['year'] as int?,
      netApe:
          json['netApe'] != null
              ? WidgetProductionSumNetApeModels.fromJson(
                json['netApe'] as Map<String, dynamic>,
              )
              : null,
    );
  }

  /// Create a copy with optional new values
  WidgetProductionSumModels copyWith({
    int? month,
    int? year,
    WidgetProductionSumNetApeModels? netApe,
  }) {
    return WidgetProductionSumModels(
      month: month ?? this.month,
      year: year ?? this.year,
      netApe: netApe ?? this.netApe,
    );
  }
}

/// Model for area total data
class WidgetProductionAreaTotalModels {
  final String? area;
  final num? total;

  WidgetProductionAreaTotalModels({this.area, this.total});

  /// Create a model from JSON map
  factory WidgetProductionAreaTotalModels.fromJson(Map<String, dynamic> json) {
    return WidgetProductionAreaTotalModels(
      area: json['area'] as String?,
      total: json['total'] as num?,
    );
  }

  /// Create a copy with optional new values
  WidgetProductionAreaTotalModels copyWith({String? area, num? total}) {
    return WidgetProductionAreaTotalModels(
      area: area ?? this.area,
      total: total ?? this.total,
    );
  }
}

/// Model for NET APE summary data
class WidgetProductionSumNetApeModels {
  final num? individu;
  final num? team;
  final num? group;
  final num? branch;
  final num? area;
  final List<WidgetProductionAreaTotalModels>? bdm;
  final List<WidgetProductionAreaTotalModels>? abdd;
  final List<WidgetProductionAreaTotalModels>? bdd;
  final List<WidgetProductionAreaTotalModels>? hos;

  WidgetProductionSumNetApeModels({
    this.individu,
    this.team,
    this.group,
    this.branch,
    this.area,
    this.bdm,
    this.abdd,
    this.bdd,
    this.hos,
  });

  /// Create a model from JSON map
  factory WidgetProductionSumNetApeModels.fromJson(Map<String, dynamic> json) {
    return WidgetProductionSumNetApeModels(
      individu: json['individu'] as num?,
      team: json['team'] as num?,
      group: json['group'] as num?,
      branch: json['branch'] as num?,
      area: json['area'] as num?,
      bdm:
          json['bdm'] != null
              ? (json['bdm'] as List<dynamic>)
                  .map(
                    (e) => WidgetProductionAreaTotalModels.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
              : null,
      abdd:
          json['abdd'] != null
              ? (json['abdd'] as List<dynamic>)
                  .map(
                    (e) => WidgetProductionAreaTotalModels.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
              : null,
      bdd:
          json['bdd'] != null
              ? (json['bdd'] as List<dynamic>)
                  .map(
                    (e) => WidgetProductionAreaTotalModels.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
              : null,
      hos:
          json['hos'] != null
              ? (json['hos'] as List<dynamic>)
                  .map(
                    (e) => WidgetProductionAreaTotalModels.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
              : null,
    );
  }

  /// Create a copy with optional new values
  WidgetProductionSumNetApeModels copyWith({
    num? individu,
    num? team,
    num? group,
    num? branch,
    num? area,
    List<WidgetProductionAreaTotalModels>? bdm,
    List<WidgetProductionAreaTotalModels>? abdd,
    List<WidgetProductionAreaTotalModels>? bdd,
    List<WidgetProductionAreaTotalModels>? hos,
  }) {
    return WidgetProductionSumNetApeModels(
      individu: individu ?? this.individu,
      team: team ?? this.team,
      group: group ?? this.group,
      branch: branch ?? this.branch,
      area: area ?? this.area,
      bdm: bdm ?? this.bdm,
      abdd: abdd ?? this.abdd,
      bdd: bdd ?? this.bdd,
      hos: hos ?? this.hos,
    );
  }
}

/// Model for production detail data
class WidgetProductionDetailModels {
  final String? distributionCode;
  final String? agentCode;
  final String? agentName;
  final String? agentPhoto;
  final String? agentLevel;
  final String? leaderCode;
  final String? type;
  final String? mainBranchCode;
  final String? branchCode;
  final String? bdmCode;
  final String? bdmName;
  final String? abddCode;
  final String? abddName;
  final String? bddCode;
  final String? bddName;
  final String? hosCode;
  final String? hosName;
  final num? year;
  final num? month;
  final String? policyNo;
  final String? policyHolderName;
  final String? policyCommDate;
  final String? status;
  final String? area;
  final String? territory;
  final String? bddRegion;
  final num? netApe;
  final num? netApi;
  final num? netCase;
  final num? netCaseTarget;
  final num? netApiTarget;

  WidgetProductionDetailModels({
    this.distributionCode,
    this.agentCode,
    this.agentName,
    this.agentPhoto,
    this.agentLevel,
    this.leaderCode,
    this.type,
    this.mainBranchCode,
    this.branchCode,
    this.bdmCode,
    this.bdmName,
    this.abddCode,
    this.abddName,
    this.bddCode,
    this.bddName,
    this.hosCode,
    this.hosName,
    this.year,
    this.month,
    this.policyNo,
    this.policyHolderName,
    this.policyCommDate,
    this.status,
    this.area,
    this.territory,
    this.bddRegion,
    this.netApe,
    this.netApi,
    this.netCase,
    this.netCaseTarget,
    this.netApiTarget,
  });

  /// Create a model from JSON map with safe type casting
  factory WidgetProductionDetailModels.fromJson(Map<String, dynamic> json) {
    return WidgetProductionDetailModels(
      distributionCode: json['distributionCode'] as String?,
      agentCode: json['agentCode'] as String?,
      agentName: json['agentName'] as String?,
      agentPhoto: json['agentPhoto'] as String?,
      agentLevel: json['agentLevel'] as String?,
      leaderCode: json['leaderCode'] as String?,
      type: json['type'] as String?,
      mainBranchCode: json['mainBranchCode'] as String?,
      branchCode: json['branchCode'] as String?,
      bdmCode: json['bdmCode'] as String?,
      bdmName: json['bdmName'] as String?,
      abddCode: json['abddCode'] as String?,
      abddName: json['abddName'] as String?,
      bddCode: json['bddCode'] as String?,
      bddName: json['bddName'] as String?,
      hosCode: json['hosCode'] as String?,
      hosName: json['hosName'] as String?,
      year: json['year'] as num?,
      month: json['month'] as num?,
      policyNo: json['policyNo'] as String?,
      policyHolderName: json['policyHolderName'] as String?,
      policyCommDate: json['policyCommDate'] as String?,
      status: json['status'] as String?,
      area: json['area'] as String?,
      territory: json['territory'] as String?,
      bddRegion: json['region'] as String? ?? json['bddRegion'] as String?,
      netApe: json['netApe'] as num?,
      netApi: json['netApi'] as num?,
      netCase: json['netCase'] as num?,
      netCaseTarget: json['netCaseTarget'] as num?,
      netApiTarget: json['netApiTarget'] as num?,
    );
  }

  /// Create a copy with optional new values
  WidgetProductionDetailModels copyWith({
    String? distributionCode,
    String? agentCode,
    String? agentName,
    String? agentPhoto,
    String? agentLevel,
    String? leaderCode,
    String? type,
    String? mainBranchCode,
    String? branchCode,
    String? bdmCode,
    String? bdmName,
    String? abddCode,
    String? abddName,
    String? bddCode,
    String? bddName,
    String? hosCode,
    String? hosName,
    num? year,
    num? month,
    String? policyNo,
    String? policyHolderName,
    String? policyCommDate,
    String? status,
    String? area,
    String? territory,
    String? bddRegion,
    num? netApe,
    num? netApi,
    num? netCase,
    num? netCaseTarget,
    num? netApiTarget,
  }) {
    return WidgetProductionDetailModels(
      distributionCode: distributionCode ?? this.distributionCode,
      agentCode: agentCode ?? this.agentCode,
      agentName: agentName ?? this.agentName,
      agentPhoto: agentPhoto ?? this.agentPhoto,
      agentLevel: agentLevel ?? this.agentLevel,
      leaderCode: leaderCode ?? this.leaderCode,
      type: type ?? this.type,
      mainBranchCode: mainBranchCode ?? this.mainBranchCode,
      branchCode: branchCode ?? this.branchCode,
      bdmCode: bdmCode ?? this.bdmCode,
      bdmName: bdmName ?? this.bdmName,
      abddCode: abddCode ?? this.abddCode,
      abddName: abddName ?? this.abddName,
      bddCode: bddCode ?? this.bddCode,
      bddName: bddName ?? this.bddName,
      hosCode: hosCode ?? this.hosCode,
      hosName: hosName ?? this.hosName,
      year: year ?? this.year,
      month: month ?? this.month,
      policyNo: policyNo ?? this.policyNo,
      policyHolderName: policyHolderName ?? this.policyHolderName,
      policyCommDate: policyCommDate ?? this.policyCommDate,
      status: status ?? this.status,
      area: area ?? this.area,
      territory: territory ?? this.territory,
      bddRegion: bddRegion ?? this.bddRegion,
      netApe: netApe ?? this.netApe,
      netApi: netApi ?? this.netApi,
      netCase: netCase ?? this.netCase,
      netCaseTarget: netCaseTarget ?? this.netCaseTarget,
      netApiTarget: netApiTarget ?? this.netApiTarget,
    );
  }

  /// Check if this model contains the search query in relevant fields
  bool containsSearchQuery(String query) {
    final searchQuery = query.toLowerCase();
    return (policyHolderName?.toLowerCase().contains(searchQuery) ?? false) ||
        (agentCode?.toLowerCase().contains(searchQuery) ?? false) ||
        (policyNo?.toLowerCase().contains(searchQuery) ?? false);
  }

  /// Check if this model's agent code contains the search query
  bool agentCodeContainsSearchQuery(String query) {
    final searchQuery = query.toLowerCase();
    return agentCode?.toLowerCase().contains(searchQuery) ?? false;
  }

  /// Check if this model's main branch code contains the search query
  bool mainBranchCodeContainsSearchQuery(String query) {
    final searchQuery = query.toLowerCase();
    return mainBranchCode?.toLowerCase().contains(searchQuery) ?? false;
  }
}
