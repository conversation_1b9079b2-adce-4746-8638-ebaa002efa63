import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/auth/reset_password_controller.dart';
import 'package:pdl_superapp/pages/authentication/first_login/base_first_login.dart';
import 'package:pdl_superapp/utils/constants.dart';

class ResetPasswordPage extends StatelessWidget {
  ResetPasswordPage({super.key});

  final ResetPasswordController controller = Get.put(ResetPasswordController());

  @override
  Widget build(BuildContext context) {
    var args = Get.arguments;
    String token = args['token'];
    controller.token = token;

    return BaseFirstLogin(
      title: 'title_create_password'.tr,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: paddingMedium,
          vertical: paddingExtraLarge,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'title_create_password'.tr,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontSize: 20),
            ),
            Text(
              'sub_title_create_password'.tr,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(height: 2),
            ),
            SizedBox(height: paddingLarge),
            PdlTextField(
              label: 'label_new_password'.tr,
              hint: 'hint_new_password'.tr,
              isPassword: true,
              textController: controller.newPasswordTextController,
              onChanged: (val) {
                controller.passwordCombinationValidator(val);
                controller.emptValidator();
              },
              borderColor:
                  controller.isConfirmSame.isFalse ? kColorError : null,
            ),
            SizedBox(height: paddingMedium),
            PdlTextField(
              label: 'label_confirm_password'.tr,
              hint: 'label_confirm_password'.tr,
              isPassword: true,
              textController: controller.confirmPasswordTextController,
              onChanged: (val) {
                controller.passwordCombinationValidator(val);
                controller.emptValidator();
              },
              borderColor:
                  controller.isConfirmSame.isFalse ? kColorError : null,
            ),
            SizedBox(height: paddingLarge),
            Text(
              'text_password_tnc'.tr,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: kColorTextTersier),
            ),
            SizedBox(height: paddingExtraSmall),
            Obx(
              () => _tncPassword(
                context,
                title: 'text_password_tnc_length'.tr,
                colors:
                    controller.is8Char.isTrue
                        ? Theme.of(context).colorScheme.primary
                        : kColorTextTersier,
                icons: Icon(
                  controller.is8Char.isTrue ? Icons.check : Icons.circle,
                  size: controller.is8Char.isTrue ? 14 : paddingSmall,
                  color:
                      controller.is8Char.isTrue
                          ? Theme.of(context).colorScheme.primary
                          : kColorTextTersier,
                ),
              ),
            ),
            SizedBox(height: paddingExtraSmall),
            Obx(
              () => _tncPassword(
                context,
                title: 'text_password_tnc_validator'.tr,
                colors:
                    controller.isValidChar.isTrue
                        ? Theme.of(context).colorScheme.primary
                        : kColorTextTersier,
                icons: Icon(
                  controller.isValidChar.isTrue ? Icons.check : Icons.circle,
                  size: controller.isValidChar.isTrue ? 14 : paddingSmall,
                  color:
                      controller.isValidChar.isTrue
                          ? Theme.of(context).colorScheme.primary
                          : kColorTextTersier,
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.only(top: paddingSmall),
              width: Get.width,
              child: Obx(
                () => FilledButton(
                  onPressed:
                      controller.isSaveAvailable.isTrue
                          ? () => controller.performSave()
                          : null,
                  style: FilledButton.styleFrom(
                    disabledBackgroundColor:
                        Get.isDarkMode ? kColorTextTersier : kColorBorderLight,
                    disabledForegroundColor: Color(0xFF6D6D6D),
                  ),
                  child: Text('button_save'.tr),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  RichText _tncPassword(
    BuildContext context, {
    required String title,
    required Color colors,
    required Icon icons,
  }) {
    return RichText(
      text: TextSpan(
        children: [
          WidgetSpan(
            child: Container(
              width: 20,
              alignment: Alignment.center,
              child: icons,
            ),
            alignment: PlaceholderAlignment.middle,
          ),
          TextSpan(
            text: title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: colors),
          ),
        ],
      ),
    );
  }
}
