import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/pages/authentication/first_login/base_first_login.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ForgotPasswordSentPage extends StatelessWidget {
  const ForgotPasswordSentPage({super.key});

  @override
  Widget build(BuildContext context) {
    var args = Get.arguments;
    String email = args['email'];
    return BaseFirstLogin(
      backEnabled: true,
      title: 'title_forgot_password'.tr,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: paddingMedium,
          vertical: paddingExtraLarge,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: Get.width / 1.5,
              child: Utils.cachedSvgWrapper(
                'icon/illustration-empty-inbox.svg',
              ),
            ),
            Text(
              'title_email_sent'.tr,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 20,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: paddingMedium),
            Text(
              'sub_title_email_sent'.trParams({'email': email}),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: paddingMedium),
            Text(
              'title_verif_not_received'.tr,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: paddingMedium),
            GestureDetector(
              onTap: () => Get.offNamed(Routes.VERIFICATION_EXPIRED),
              child: Text(
                'title_resent'.tr,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
