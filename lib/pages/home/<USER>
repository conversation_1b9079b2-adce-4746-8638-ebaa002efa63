import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/widget/polis_lapse_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:pdl_superapp/routes/app_routes.dart';

class PolisLapsedWidget extends StatelessWidget {
  PolisLapsedWidget({super.key});

  // Use GetX controller
  final PolisLapseController controller = Get.put(
    PolisLapseController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }
      return Column(
        spacing: paddingMedium,
        children: [
          // Show tabs for non-BP roles
          if (controller.level != kLevelBP) _buildTabSelector(),

          // Content based on role and selected tab
          _buildContent(),
        ],
      );
    });
  }

  // Tab selector for non-BP roles
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(200),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToIndividu(),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        controller.selectedSection.value == 0
                            ? Colors.white
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(200),
                    border: Border.all(
                      color:
                          controller.selectedSection.value == 0
                              ? Colors.grey
                              : Colors.transparent,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Individu',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                      color:
                          controller.selectedSection.value == 0
                              ? Colors.black
                              : Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToTeam(),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        controller.selectedSection.value == 1
                            ? Colors.white
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(200),
                    border: Border.all(
                      color:
                          controller.selectedSection.value == 1
                              ? Colors.grey
                              : Colors.transparent,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    controller.level == kLevelBD ? 'Group' : 'Team',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                      color:
                          controller.selectedSection.value == 1
                              ? Colors.black
                              : Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // For BP role, show only individu content
    if (controller.level == kLevelBP) {
      return _buildIndividuContent();
    }

    // For other roles, show content based on selected tab
    return Obx(() {
      if (controller.selectedSection.value == 0) {
        return _buildIndividuContent();
      } else {
        return _buildTeamContent();
      }
    });
  }

  // Individu Content
  Widget _buildIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasError.value;
      final errorMessage = controller.individuController.errorMessage.value;

      void onRetry() {
        controller.individuController.fetchPolicyLapsedData();
      }

      if (isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (hasError) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: $errorMessage'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('Coba Lagi'),
              ),
            ],
          ),
        );
      }

      if (controller.individuController.policyLapsedList.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("Tidak ada data polis lapse"),
          ),
        );
      }

      // Show only first 3 records
      final displayedPolicies =
          controller.individuController.policyLapsedList.length > 3
              ? controller.individuController.policyLapsedList.sublist(0, 3)
              : controller.individuController.policyLapsedList;

      return Column(
        children: [
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayedPolicies.length,
            padding: EdgeInsets.zero,
            separatorBuilder:
                (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: paddingMedium),
                  child: const Divider(height: 1),
                ),
            itemBuilder: (context, index) {
              final policy = displayedPolicies[index];
              return PolicyLapseItem(
                name: policy.policyHolderName,
                policyNumber: policy.policyNumber,
                dueDate: formatDate(policy.lastPaidDate),
                status: policy.policyStatus,
                showAgentInfo: false,
              );
            },
          ),

          Container(
            margin: EdgeInsets.only(top: paddingMedium),
            width: double.infinity,
            child: PdlButton(
              title: 'Lihat Detail',
              onPressed: () {
                final agentCode =
                    controller.prefs.getString(kStorageAgentCode) ?? '';
                Get.toNamed(
                  '${Routes.POLIS_LAPSE}?agentCode=$agentCode&withDownline=0',
                );
              },
            ),
          ),

          // Lihat Detail and Refresh buttons
          TextButton(
            onPressed: onRetry,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: const [
                Icon(Icons.refresh),
                SizedBox(width: paddingSmall),
                Text("Perbarui"),
              ],
            ),
          ),
        ],
      );
    });
  }

  // Team Content
  Widget _buildTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasError.value;
      final errorMessage = controller.teamController.errorMessage.value;

      void onRetry() {
        controller.teamController.fetchPolicyLapsedData();
      }

      if (isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (hasError) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: $errorMessage'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('Coba Lagi'),
              ),
            ],
          ),
        );
      }

      if (controller.teamController.policyLapsedList.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("Tidak ada data polis lapse"),
          ),
        );
      }

      // Show only first 3 records
      final displayedPolicies =
          controller.teamController.policyLapsedList.length > 3
              ? controller.teamController.policyLapsedList.sublist(0, 3)
              : controller.teamController.policyLapsedList;

      return Column(
        children: [
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayedPolicies.length,
            padding: EdgeInsets.zero,
            separatorBuilder:
                (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: paddingMedium),
                  child: const Divider(height: 1, thickness: 3),
                ),
            itemBuilder: (context, index) {
              final policy = displayedPolicies[index];
              return PolicyLapseItem(
                name: policy.policyHolderName,
                policyNumber: policy.policyNumber,
                dueDate: formatDate(policy.lastPaidDate),
                status: policy.policyStatus,
                showAgentInfo: true,
                agentName: policy.agentName,
                agentCode: policy.agentCode,
              );
            },
          ),

          // Lihat Detail and Refresh buttons
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(top: paddingMedium),
            padding: const EdgeInsets.symmetric(vertical: paddingMedium),
            child: PdlButton(
              title: 'Lihat Detail',
              onPressed: () {
                final agentCode =
                    controller.prefs.getString(kStorageAgentCode) ?? '';
                Get.toNamed(
                  '${Routes.POLIS_LAPSE}?agentCode=$agentCode&withDownline=1',
                );
              },
            ),
          ),
          TextButton(
            onPressed: onRetry,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: const [
                Icon(Icons.refresh),
                SizedBox(width: paddingSmall),
                Text("Perbarui"),
              ],
            ),
          ),
        ],
      );
    });
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yy').format(date);
    } catch (e) {
      return dateString;
    }
  }
}

class PolicyLapseItem extends StatelessWidget {
  final String name;
  final String policyNumber;
  final String dueDate;
  final String status;
  final bool showAgentInfo;
  final String? agentName;
  final String? agentCode;

  const PolicyLapseItem({
    super.key,
    required this.name,
    required this.policyNumber,
    required this.dueDate,
    required this.status,
    this.showAgentInfo = false,
    this.agentName,
    this.agentCode,
  });

  @override
  Widget build(BuildContext context) {
    // Define the fields to display
    final List<Map<String, dynamic>> fields = [
      {'label': 'Pemegang Polis', 'value': name, 'isStatus': false},
      {'label': 'No. Polis', 'value': policyNumber, 'isStatus': false},
      {'label': 'Tanggal Jatuh Tempo', 'value': dueDate, 'isStatus': false},
      {'label': 'Status', 'value': status, 'isStatus': true},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: paddingSmall,
      children: [
        // Agent info section (if applicable)
        if (showAgentInfo && agentName != null && agentCode != null)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                agentName!,
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                agentCode!,
                style: const TextStyle(color: Colors.black87, fontSize: 14),
              ),
              const Divider(),
            ],
          ),

        // Generate rows for each field using a loop
        for (int i = 0; i < fields.length; i++) ...[
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                fields[i]['label'],
                style: const TextStyle(color: Colors.grey, fontSize: 16),
              ),
              const Spacer(),
              if (fields[i]['isStatus'] == true)
                // Status badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(fields[i]['value']),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getDisplayStatus(fields[i]['value']),
                    style: TextStyle(
                      color: _getStatusTextColor(fields[i]['value']),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              else
                // Regular text value
                Text(
                  fields[i]['value'],
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
        ],
      ],
    );
  }

  // Get status display text
  String _getDisplayStatus(String status) {
    switch (status) {
      case "ENDING":
        return "Akan Jatuh Tempo";
      case "LAPSE":
        return "Lapse";
      case "ACTIVE":
        return "Aktif";
      default:
        return status;
    }
  }

  // Get status color
  Color _getStatusColor(String status) {
    switch (status) {
      case "ENDING":
        return const Color(0xFFFDF1E0); // Yellow background
      case "LAPSE":
        return const Color(0xFFFFE5E5); // Red background
      case "ACTIVE":
        return const Color(0xFFE0F2F1); // Green background
      default:
        return const Color(0xFFE3F2FD); // Blue background
    }
  }

  // Get status text color
  Color _getStatusTextColor(String status) {
    switch (status) {
      case "ENDING":
        return const Color(0xFFAD7B2A); // Yellow text
      case "LAPSE":
        return const Color(0xFFD32F2F); // Red text
      case "ACTIVE":
        return const Color(0xFF00897B); // Green text
      default:
        return const Color(0xFF1976D2); // Blue text
    }
  }
}
