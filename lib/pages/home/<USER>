import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/widget/birthday_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class BirthdayWidget extends StatelessWidget {
  BirthdayWidget({super.key});

  // Use GetX controller
  final BirthdayController controller = Get.put(
    BirthdayController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.hasError.value) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: ${controller.errorMessage.value}'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(
                onPressed: () => controller.fetchBirthdayData(),
                child: const Text('Coba Lagi'),
              ),
            ],
          ),
        );
      }

      if (controller.birthdayList.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("Tidak ada data ulang tahun"),
          ),
        );
      }

      // Show only first 5 records
      final displayedBirthdays =
          controller.birthdayList.length > 5
              ? controller.birthdayList.sublist(0, 5)
              : controller.birthdayList;

      return Column(
        children: [
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayedBirthdays.length,
            padding: EdgeInsets.zero,
            separatorBuilder:
                (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: paddingMedium),
                  child: const Divider(height: 1),
                ),
            itemBuilder: (context, index) {
              final birthday = displayedBirthdays[index];
              return ContactItem(
                name: birthday.customerName,
                date: controller.formatDate(birthday.birthday),
                initial: controller.getInitial(birthday.relation),
                id: birthday.policyNumber,
                phoneNumber: birthday.phoneNumber,
                controller: controller,
              );
            },
          ),

          // Lihat Detail button
          Container(
            margin: EdgeInsets.only(top: paddingMedium),
            width: double.infinity,
            child: PdlButton(
              title: 'Lihat Detail',
              onPressed: () {
                Get.toNamed(Routes.BIRTHDAY);
              },
            ),
          ),

          // Refresh button
          TextButton(
            onPressed: () => controller.fetchBirthdayData(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: const [
                Icon(Icons.refresh),
                SizedBox(width: paddingSmall),
                Text("Perbarui"),
              ],
            ),
          ),
        ],
      );
    });
  }
}

class ContactItem extends StatelessWidget {
  final String name;
  final String date;
  final String initial;
  final String id;
  final String phoneNumber;
  final BirthdayController controller;

  const ContactItem({
    super.key,
    required this.name,
    required this.date,
    required this.initial,
    required this.id,
    this.phoneNumber = '',
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: paddingMedium,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: paddingSmall,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                date,
                style: const TextStyle(fontSize: 16, color: Colors.black87),
              ),
              Row(
                spacing: paddingMedium,
                children: [
                  Container(
                    width: 36,
                    height: 36,
                    decoration: const BoxDecoration(
                      color: Color(0xFFF0F0F0),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        initial,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  Text(
                    id,
                    style: const TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
        Container(
          margin: const EdgeInsets.only(top: 8),
          child: OutlinedButton(
            onPressed: () => controller.sendBirthdayGreeting(phoneNumber, name),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: Colors.blue),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(radiusSmall),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: const Text("Kirim Ucapan"),
          ),
        ),
      ],
    );
  }
}
