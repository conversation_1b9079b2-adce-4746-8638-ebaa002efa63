import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/components/claim_item.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/widget/claim_widget_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ClaimWidget extends StatelessWidget {
  ClaimWidget({super.key});

  // Use GetX controller
  final ClaimWidgetController controller = Get.put(
    ClaimWidgetController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Show tabs for non-BP roles
          if (controller.level != kLevelBP) _buildTabSelector(),

          // Content based on role and selected tab
          _buildContent(),
        ],
      );
    });
  }

  // Tab selector for non-BP roles
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(200),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      margin: EdgeInsets.only(bottom: paddingMedium),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToIndividu(),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        controller.selectedSection.value == 0
                            ? Colors.white
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(200),
                    border: Border.all(
                      color:
                          controller.selectedSection.value == 0
                              ? Colors.grey
                              : Colors.transparent,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Individu',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                      color:
                          controller.selectedSection.value == 0
                              ? Colors.black
                              : Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToTeam(),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        controller.selectedSection.value == 1
                            ? Colors.white
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(200),
                    border: Border.all(
                      color:
                          controller.selectedSection.value == 1
                              ? Colors.grey
                              : Colors.transparent,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    controller.level == kLevelBD ? 'Group' : 'Team',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                      color:
                          controller.selectedSection.value == 1
                              ? Colors.black
                              : Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // For BP role, show only individu content
    if (controller.level == kLevelBP) {
      return _buildIndividuContent();
    }

    // For other roles, show content based on selected tab
    return Obx(() {
      if (controller.selectedSection.value == 0) {
        return _buildIndividuContent();
      } else {
        return _buildTeamContent();
      }
    });
  }

  // Individu Content
  Widget _buildIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasClaimError.value;
      final errorMessage =
          controller.individuController.claimErrorMessage.value;

      void onRetry() {
        controller.individuController.fetchClaimTrackingData();
      }

      if (isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (hasError) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: $errorMessage'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('Coba Lagi'),
              ),
            ],
          ),
        );
      }

      if (controller.individuController.claimTrackingList.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("Tidak ada data tracking klaim"),
          ),
        );
      }

      // Show only first 3 records
      final displayedClaims =
          controller.individuController.claimTrackingList.length > 3
              ? controller.individuController.claimTrackingList.sublist(0, 3)
              : controller.individuController.claimTrackingList;

      return Column(
        children: [
          ListView.separated(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayedClaims.length,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              final claim = displayedClaims[index];
              return ClaimItem(
                claimId: claim.claimId,
                policyHolder: claim.policyHolder,
                policyNumber: claim.policyNumber,
                issueDate: formatDate(claim.requestedDate),
                claimAmount: formatCurrency(claim.amount, claim.currency),
                note: claim.remark,
                bulletNotes: claim.diagnose.isNotEmpty ? [claim.diagnose] : [],
                status: claim.claimStatus,
              );
            },
          ),

          SizedBox(height: paddingSmall),
          SizedBox(
            width: double.infinity,
            child: PdlButton(
              onPressed:
                  () => Get.toNamed(
                    '${Routes.CLAIM}?agentCode=${controller.prefs.getString(kStorageAgentCode)}&withDownline=0',
                  ),
              title: "Lihat Detail",
            ),
          ),
          SizedBox(height: paddingSmall),
          TextButton(
            onPressed: () => onRetry(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: const [
                Icon(Icons.refresh),
                SizedBox(width: paddingSmall),
                Text("Perbarui"),
              ],
            ),
          ),
        ],
      );
    });
  }

  // Team Content
  Widget _buildTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasClaimError.value;
      final errorMessage = controller.teamController.claimErrorMessage.value;

      void onRetry() {
        controller.teamController.fetchClaimTrackingData();
      }

      if (isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (hasError) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: $errorMessage'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('Coba Lagi'),
              ),
            ],
          ),
        );
      }

      if (controller.teamController.claimTrackingList.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("Tidak ada data tracking klaim"),
          ),
        );
      }

      // Show only first 3 records
      final displayedClaims =
          controller.teamController.claimTrackingList.length > 3
              ? controller.teamController.claimTrackingList.sublist(0, 3)
              : controller.teamController.claimTrackingList;

      return Column(
        children: [
          ListView.separated(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayedClaims.length,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              final claim = displayedClaims[index];
              return ClaimItem(
                claimId: claim.claimId,
                policyHolder: claim.policyHolder,
                policyNumber: claim.policyNumber,
                issueDate: formatDate(claim.requestedDate),
                claimAmount: formatCurrency(claim.amount, claim.currency),
                note: claim.remark,
                bulletNotes: claim.diagnose.isNotEmpty ? [claim.diagnose] : [],
                status: claim.claimStatus,
              );
            },
          ),

          SizedBox(height: paddingSmall),
          SizedBox(
            width: double.infinity,
            child: PdlButton(
              onPressed:
                  () => Get.toNamed(
                    '${Routes.CLAIM}?agentCode=${controller.prefs.getString(kStorageAgentCode)}&withDownline=1',
                  ),
              title: "Lihat Detail",
            ),
          ),
          SizedBox(height: paddingSmall),
          TextButton(
            onPressed: () => onRetry(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: const [
                Icon(Icons.refresh),
                SizedBox(width: paddingSmall),
                Text("Perbarui"),
              ],
            ),
          ),
        ],
      );
    });
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  // Format currency
  String formatCurrency(double amount, String currency) {
    final formatter = NumberFormat.currency(symbol: currency, decimalDigits: 2);
    return formatter.format(amount);
  }
}

// Example usage
class ClaimScreenExample extends StatelessWidget {
  const ClaimScreenExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: ClaimWidget(),
        ),
      ),
    );
  }
}
