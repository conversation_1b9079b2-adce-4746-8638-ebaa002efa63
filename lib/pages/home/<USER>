import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/table_card.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_widget_controller.dart';
import 'package:pdl_superapp/models/persistensi_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PersistensiWidget extends StatelessWidget {
  // Use GetX controller
  final PersistensiWidgetController controller = Get.put(
    PersistensiWidgetController(),
    tag: Utils.getRandomString(),
  );

  PersistensiWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }
      return Column(
        children: [
          // Show tabs for non-BP roles or BD roles
          if (controller.level != kLevelBP) _buildTabSelector(),

          // Content based on role and selected tab
          _buildContent(),
        ],
      );
    });
  }

  // Tab selector for non-BP roles
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(200),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToIndividu(),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        controller.selectedSection.value == 0
                            ? Colors.white
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(200),
                    border: Border.all(
                      color:
                          controller.selectedSection.value == 0
                              ? Colors.grey
                              : Colors.transparent,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Individu',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                      color:
                          controller.selectedSection.value == 0
                              ? Colors.black
                              : Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToTeam(),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        controller.selectedSection.value == 1
                            ? Colors.white
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(200),
                    border: Border.all(
                      color:
                          controller.selectedSection.value == 1
                              ? Colors.grey
                              : Colors.transparent,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    controller.level == kLevelBD ? 'Group' : 'Team',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                      color:
                          controller.selectedSection.value == 1
                              ? Colors.black
                              : Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // For BP role, show only individu content
    if (controller.level == kLevelBP) {
      return _buildIndividuContent();
    }

    // For other roles, show content based on selected tab
    return Obx(() {
      if (controller.selectedSection.value == 0) {
        return _buildIndividuContent();
      } else {
        return _buildTeamContent();
      }
    });
  }

  // Individu Content
  Widget _buildIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasError.value;
      final errorMessage = controller.individuController.errorMessage.value;

      void onRetry() {
        controller.individuController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
            else if (controller.individuController.persistensiData.value ==
                null)
              const Center(child: Text('No data available'))
            else
              _buildPersistencyData(
                controller.individuController.persistensiData.value!,
              ),

            // Refresh Button
            if (!isLoading)
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: const [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text("Perbarui"),
                    ],
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  // Team Content
  Widget _buildTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasError.value;
      final errorMessage = controller.teamController.errorMessage.value;

      void onRetry() {
        controller.teamController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
            else if (controller.teamController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAgentPersistencyTables(),

            // Refresh Button
            if (!isLoading)
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: const [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text("Perbarui"),
                    ],
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  // Build persistency data display
  Widget _buildPersistencyData(PersistensiModel data) {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPersistenceRow(
          'Persistensi-13',
          controller.individuController.formatPercentage(data.persistency13),
        ),
        _buildPersistenceRow(
          'Persistensi-25',
          controller.individuController.formatPercentage(data.persistency25),
        ),
        _buildPersistenceRow(
          'Persistensi-37',
          controller.individuController.formatPercentage(data.persistency37),
        ),
        _buildPersistenceRow(
          'Persistensi-49',
          controller.individuController.formatPercentage(data.persistency49),
        ),
        _buildPersistenceRow(
          'Persistensi-61',
          controller.individuController.formatPercentage(data.persistency61),
        ),
      ],
    );
  }

  // Build tables for each agent in the team/group
  Widget _buildAgentPersistencyTables() {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          controller.teamController.persistensiDataList.map((agent) {
            return _buildAgentPersistencyTable(agent);
          }).toList(),
    );
  }

  // Build a table card for a single agent's persistency data
  Widget _buildAgentPersistencyTable(PersistensiModel agent) {
    String agentType = "";
    switch (agent.type) {
      case "leader":
        agentType = "BM";
        break;
      case "agent":
        agentType = "BD";
        break;
      default:
        agentType = "BP";
        break;
    }
    final agentTitle = "$agentType ${agent.name}";
    return TableCard(
      title: agentTitle,
      headers: ["P-13", "P-25", "P-37", "P-49", "P-61"],
      rows: [
        [
          controller.teamController.formatPercentage(agent.persistency13),
          controller.teamController.formatPercentage(agent.persistency25),
          controller.teamController.formatPercentage(agent.persistency37),
          controller.teamController.formatPercentage(agent.persistency49),
          controller.teamController.formatPercentage(agent.persistency61),
        ],
      ],
      // Enable horizontal scrolling
      horizontalScrollable: true,
      // Set minimum column widths
      minColumnWidths: {
        0: 80.0, // P-13 column
        1: 80.0, // P-25 column
        2: 80.0, // P-37 column
        3: 80.0, // P-49 column
        4: 80.0, // P-61 column
      },
    );
  }

  Widget _buildPersistenceRow(String title, String percentage) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 16, color: Color(0xFF333333)),
        ),
        Text(
          percentage,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Color(0xFF333333),
          ),
        ),
      ],
    );
  }
}
