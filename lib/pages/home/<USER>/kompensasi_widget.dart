import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/widget/kompensasi_controller.dart';
import 'package:pdl_superapp/models/kompensasi_item_model.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class KompensasiWidget extends StatelessWidget {
  KompensasiWidget({super.key});

  // Use GetX controller
  final KompensasiController controller = Get.put(
    KompensasiController(),
    tag: Utils.getRandomString(),
  );

  // Build a table row from a KompensasiItemModel
  TableRow _buildTableRow(KompensasiItemModel item) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: paddingSmall),
          child: Text(
            item.label,
            style: TextStyle(
              fontWeight: item.isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: paddingSmall),
          child: Text(
            item.value,
            textAlign: TextAlign.end,
            style: TextStyle(
              fontWeight: item.isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Periode
        Obx(
          () => Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(radiusSmall),
              color: Colors.grey.shade200,
            ),
            padding: const EdgeInsets.all(paddingSmall),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  controller.periode.value,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: paddingSmall),
                Text(controller.periodeTanggal.value),
              ],
            ),
          ),
        ),
        const SizedBox(height: paddingMedium),

        // Estimasi Komisi Berjalan
        Obx(() {
          if (controller.isLoading.value) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: CircularProgressIndicator(),
              ),
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Estimasi Komisi Berjalan',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: paddingSmall),

              // Dynamic Table
              Obx(
                () => Table(
                  columnWidths: const {
                    0: FlexColumnWidth(2),
                    1: FlexColumnWidth(1),
                  },
                  children: [
                    // Generate rows for each item
                    ...controller.items.map((item) => _buildTableRow(item)),
                    // Total row
                    _buildTableRow(
                      KompensasiItemModel(
                        label: 'Total',
                        value: controller.totalValue.value,
                        isTotal: true,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        }),
        const SizedBox(height: paddingMedium),

        // Lihat Detail
        Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: PdlButton(
                title: "Lihat Detail",
                onPressed: () => Get.toNamed(Routes.KOMPENSASI),
              ),
            ),
            const SizedBox(height: paddingSmall),
            TextButton(
              onPressed: () => controller.refreshData(),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.refresh),
                  const SizedBox(width: paddingSmall),
                  const Text("Perbarui"),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
