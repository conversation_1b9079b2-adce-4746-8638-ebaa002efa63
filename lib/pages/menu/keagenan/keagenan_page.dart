import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/custom_card_agency.dart';
import 'package:pdl_superapp/components/filter_button.dart';
import 'package:pdl_superapp/components/icon_menu.dart';
import 'package:pdl_superapp/components/method_bottom_sheet_card.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/keagenan_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class KeagenanPage extends StatelessWidget {
  KeagenanPage({super.key});

  final KeagenanController controller = Get.put(KeagenanController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Keagenan',
      isActionActive: true,
      controller: controller,
      onRefresh: () {},
      backEnabled: true,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _title('Keagenan'),
          GridView.count(
            crossAxisCount: 4,
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            children: [
              IconMenu(
                onTap: () => Get.toNamed(Routes.KEAGENAN_LIST),
                iconUrl: 'icon/ic-menu-sub-rekrut.svg',
                title: 'Rekrut',
              ),
              IconMenu(
                onTap:
                    () => PdlBottomSheet(
                      content: _methodRejoinBottomSheet(),
                      title: 'Pilih Menu',
                    ),
                iconUrl: 'icon/ic-menu-sub-rejoin.svg',
                title: 'Bergabung Kembali',
              ),
            ],
          ),
          SizedBox(height: paddingMedium),
          Divider(thickness: 6),
          _productivity(context),
          SizedBox(height: paddingMedium),
          Divider(thickness: 6),
          _sliderCard(),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: paddingMedium),
            child: TitleWidget(title: 'Daftar Kandidat Rekrut'),
          ),
          SizedBox(height: paddingMedium),
          _searchBar(),
          SizedBox(height: paddingMedium),
          // Placeholder untuk daftar rekrutmen
          Text(
            'Lihat daftar lengkap di halaman Rekrut',
            style: Get.textTheme.bodyMedium?.copyWith(
              color:
                  Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  SizedBox _methodRejoinBottomSheet() {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          SizedBox(height: paddingMedium),
          MethodBottomSheetCard(
            iconUrl: 'icon/ic-menu-rejoin-request.svg',
            title: 'Daftar Pengajuan Bergabung Kembali',
            description: 'Lihat daftar agen yang Anda ajak bergabung kembali',
            onTap: () {
              Get.back();
              Get.toNamed(Routes.REJOIN);
            },
          ),
          SizedBox(height: paddingMedium),
          MethodBottomSheetCard(
            iconUrl: 'icon/ic-menu-rejoin-bp.svg',
            title: 'Bergabung Kembali BP',
            description: 'Pilih agen BP untuk Anda ajak bergabung kembali',
            onTap: () {
              Get.back();
              Get.toNamed(Routes.REJOIN_CHOOSE_USER);
            },
          ),
          SizedBox(height: paddingMedium),
        ],
      ),
    );
  }

  Container _searchBar() {
    return Container(
      width: Get.width,
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: PdlTextField(
              hint: 'Cari nama atau kode',
              onChanged: (value) {},
              prefixIcon: Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-search -2.svg',
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                ),
              ),
            ),
          ),
          SizedBox(width: paddingSmall),
          FilterButton(content: Container(), title: 'Filter Daftar Kandidat'),
        ],
      ),
    );
  }

  SizedBox _sliderCard() {
    return SizedBox(
      width: Get.width,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Wrap(
          spacing: paddingMedium,
          children: [
            Container(),
            Container(
              width: Get.width / 2.5,
              padding: EdgeInsets.symmetric(vertical: paddingMedium),
              child: CustomCardAgencyWidget(
                title: 'Rekrut',
                startColor: Color(0xFFE6CFFF),
                endColor: Color(0xFFBA83F6),
                selected: 'Rekrut',
              ),
            ),
            Container(
              width: Get.width / 2.5,
              padding: EdgeInsets.symmetric(vertical: paddingMedium),
              child: CustomCardAgencyWidget(
                title: 'Promosi',
                startColor: Color(0xFFF6D436),
                endColor: Color(0xFFFFA751),
                selected: 'Rekrut',
              ),
            ),
            Container(
              width: Get.width / 2.5,
              padding: EdgeInsets.symmetric(vertical: paddingMedium),
              child: CustomCardAgencyWidget(
                title: 'Demosi',
                startColor: Color(0xFFF6D436),
                endColor: Color(0xFFFFA751),
                selected: 'Rekrut',
              ),
            ),
            Container(),
          ],
        ),
      ),
    );
  }

  Container _productivity(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TitleWidget(title: 'Produktivitas Rekrut'),
                Text(
                  'Berkode Agen (Bulan)',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    height: 2,
                    color:
                        Get.isDarkMode
                            ? kColorTextTersier
                            : kColorTextTersierLight,
                  ),
                ),
              ],
            ),
          ),
          FilterButton(
            title: 'Filter Produktivitas',
            notificationCount: 2,
            content: Column(
              children: [
                SizedBox(height: paddingMedium),
                Container(
                  width: Get.width,
                  padding: EdgeInsets.only(
                    left: paddingMedium,
                    right: paddingMedium,
                    top: paddingMedium,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.05),
                        blurRadius: 10, // Softness of the shadow
                        spreadRadius: 0, // How much the shadow spreads
                        offset: Offset(0, -8), // Shadow position
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 0),
                    child: Row(
                      children: [
                        Expanded(
                          child: PdlButton(
                            title: "Hapus Filter",
                            borderColor: Colors.transparent,
                            backgroundColor: Colors.transparent,
                            foregorundColor:
                                Get.isDarkMode
                                    ? kColorTextDark
                                    : kColorTextLight,
                            onPressed: () => Get.back(),
                          ),
                        ),
                        SizedBox(width: paddingMedium),
                        Expanded(
                          child: PdlButton(
                            title: "Terapkan",
                            onPressed: () => Get.back(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Container _title(String title) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingMedium),
      child: TitleWidget(title: title),
    );
  }
}
