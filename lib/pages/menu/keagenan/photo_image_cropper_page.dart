import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

class PhotoImageCropperPage extends StatefulWidget {
  const PhotoImageCropperPage({super.key});

  @override
  State<PhotoImageCropperPage> createState() => _PhotoImageCropperPageState();
}

class _PhotoImageCropperPageState extends State<PhotoImageCropperPage> {
  final File imageFile = Get.arguments['imageFile'] as File;
  String type = Get.arguments['type'] ?? '';

  CroppedFile? croppedFile;
  bool isCropping = false;

  @override
  void initState() {
    super.initState();
    // Start cropping when the page loads
    _cropImage();
  }

  Future<void> _cropImage() async {
    setState(() {
      isCropping = true;
    });

    try {
      // KTP card dimensions ratio (85.6 x 53.98 mm)
      double ktpAspectRatio = 3 / 4;

      if (type == kPhotoTypeKtp) {
        ktpAspectRatio = 85.6 / 53.98;
      } else if (type == kPhotoTypePasFoto) {
        ktpAspectRatio = 1;
      }

      final croppedImage = await ImageCropper().cropImage(
        sourcePath: imageFile.path,
        aspectRatio: CropAspectRatio(ratioX: ktpAspectRatio, ratioY: 1),
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Sesuaikan Ukuran Foto',
            toolbarColor: kColorGlobalBlue,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.original,
            lockAspectRatio: true,
            hideBottomControls: false,
          ),
          IOSUiSettings(
            title: 'Sesuaikan Ukuran Foto',
            aspectRatioLockEnabled: true,
            resetAspectRatioEnabled: false,
            aspectRatioPickerButtonHidden: true,
          ),
        ],
      );

      if (croppedImage != null) {
        setState(() {
          croppedFile = croppedImage;
          isCropping = false;
        });
      } else {
        // User canceled cropping, go back
        Get.back();
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to crop image: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
      setState(() {
        isCropping = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Sesuaikan Ukuran Foto',
      onRefresh: () {},
      controller: Get.find<RecruitmentFormController>(),
      bottomWidget: Row(
        children: [
          Expanded(
            child: PdlButton(
              title: 'Ulangi',
              onPressed: () => _cropImage(),
              backgroundColor: Colors.transparent,
              foregorundColor: kColorPaninBlue,
            ),
          ),
          SizedBox(width: paddingSmall),
          Expanded(
            child: PdlButton(
              title: 'Gunakan Foto',
              onPressed: () {
                if (croppedFile != null) {
                  // Return the cropped file
                  Get.back(result: File(croppedFile!.path));
                }
              },
            ),
          ),
        ],
      ),
      child:
          isCropping
              ? const Center(child: CircularProgressIndicator())
              : croppedFile != null
              ? Container(
                padding: EdgeInsets.all(paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pastikan foto sudah sesuai dengan ukuran yang diinginkan',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    SizedBox(height: paddingMedium),
                    Container(
                      width: Get.width,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(radiusMedium),
                        border: Border.all(color: kColorGlobalBlue, width: 2.0),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(radiusMedium - 2),
                        child: Image.file(
                          File(croppedFile!.path),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ],
                ),
              )
              : const SizedBox(),
    );
  }
}
