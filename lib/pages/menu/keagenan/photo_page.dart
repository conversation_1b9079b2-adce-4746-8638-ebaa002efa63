import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/photo_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

// ignore: must_be_immutable
class PhotoPage extends StatelessWidget {
  PhotoPage({super.key});

  final PdlPHotoController controller = Get.put(PdlPHotoController());

  // KTP card dimensions ratio (85.6 x 53.98 mm)
  double ktpAspectRatio = 85.6 / 53.98;

  String title = Get.arguments['title'];
  String type = Get.arguments['type'] ?? '';

  @override
  Widget build(BuildContext context) {
    if (type == kPhotoTypeSelfieKtp) {
      ktpAspectRatio = 3 / 4;
    } else if (type == kPhotoTypePasFoto) {
      ktpAspectRatio = 1;
    }
    return BaseDetailPage(
      title: 'Foto $title',
      controller: controller,
      onRefresh: () {},
      bottomAction: () {
        controller.isTakingPicture.value ? null : controller.takePicture();
      },
      bottomText: 'Ambil Foto',
      child: SizedBox(
        height: Get.height - Get.statusBarHeight - paddingExtraLarge,
        child: Column(
          children: [
            SizedBox(height: paddingMedium),
            Container(
              width: Get.width,
              padding: EdgeInsets.symmetric(horizontal: paddingMedium),
              child: TitleWidget(title: 'Ambil Foto $title'),
            ),
            Container(
              width: Get.width,
              padding: EdgeInsets.symmetric(horizontal: paddingMedium),
              child: Text('Ambil foto sesuai tanda yang tersedia'),
            ),
            Expanded(child: Container()),
            Container(
              clipBehavior: Clip.hardEdge,
              width: Get.width - paddingMedium,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(radiusMedium),
              ),
              height: Get.height / 1.5,
              child: _buildCameraUI(context),
            ),
            Expanded(child: Container()),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraUI(BuildContext context) {
    return Obx(() {
      if (!controller.isCameraInitialized.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return Stack(
        children: [
          // Camera preview
          SizedBox(
            width: Get.width,
            height: Get.height,
            child: CameraPreview(controller.cameraController.value!),
          ),

          // Semi-transparent overlay with cutout
          if (type != '') _buildOverlayWithCutout(context),
        ],
      );
    });
  }

  Widget _buildOverlayWithCutout(BuildContext context) {
    // Calculate KTP cutout dimensions
    final screenWidth = MediaQuery.of(context).size.width;
    final cutoutWidth = screenWidth * 0.85; // 85% of screen width
    final cutoutHeight = cutoutWidth / ktpAspectRatio;

    return Stack(
      children: [
        // Semi-transparent gray overlay with cutout
        ClipPath(
          clipper: CutoutClipper(
            cutoutWidth: cutoutWidth,
            cutoutHeight: cutoutHeight,
            borderRadius: 8,
          ),
          child: Container(
            width: Get.width,
            height: Get.height,
            color: Colors.black.withValues(alpha: 0.5),
          ),
        ),

        // Blue border for the cutout
        Center(
          child: Container(
            width: cutoutWidth,
            height: cutoutHeight,
            decoration: BoxDecoration(
              color: Colors.transparent,
              border: Border.all(color: kColorGlobalBlue, width: 2.0),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Stack(
              children: [
                // Corner guides
                _buildCornerGuide(Alignment.topLeft),
                _buildCornerGuide(Alignment.topRight),
                _buildCornerGuide(Alignment.bottomLeft),
                _buildCornerGuide(Alignment.bottomRight),

                // Instruction text
                if (type == kPhotoTypeKtp)
                  Positioned(
                    bottom: 10,
                    left: 0,
                    right: 0,
                    child: Text(
                      'Posisikan KTP di dalam bingkai',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            offset: Offset(1, 1),
                            blurRadius: 3,
                            color: Colors.black.withValues(alpha: 0.5),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCornerGuide(Alignment alignment) {
    // Corner guide size
    const double size = 20;
    const double thickness = 3;

    // Determine position based on alignment
    double? top = alignment.y < 0 ? 0 : null;
    double? bottom = alignment.y > 0 ? 0 : null;
    double? left = alignment.x < 0 ? 0 : null;
    double? right = alignment.x > 0 ? 0 : null;

    return Positioned(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
      child: SizedBox(
        width: size,
        height: size,
        child: CustomPaint(
          painter: CornerPainter(
            color: kColorGlobalBlue,
            thickness: thickness,
            isTopLeft: alignment == Alignment.topLeft,
            isTopRight: alignment == Alignment.topRight,
            isBottomLeft: alignment == Alignment.bottomLeft,
            isBottomRight: alignment == Alignment.bottomRight,
          ),
        ),
      ),
    );
  }
}

// Custom painter for corner guides
class CornerPainter extends CustomPainter {
  final Color color;
  final double thickness;
  final bool isTopLeft;
  final bool isTopRight;
  final bool isBottomLeft;
  final bool isBottomRight;

  CornerPainter({
    required this.color,
    required this.thickness,
    this.isTopLeft = false,
    this.isTopRight = false,
    this.isBottomLeft = false,
    this.isBottomRight = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.stroke
          ..strokeWidth = thickness
          ..strokeCap = StrokeCap.round;

    final path = Path();

    if (isTopLeft) {
      path.moveTo(0, size.height / 2);
      path.lineTo(0, 0);
      path.lineTo(size.width / 2, 0);
    } else if (isTopRight) {
      path.moveTo(size.width / 2, 0);
      path.lineTo(size.width, 0);
      path.lineTo(size.width, size.height / 2);
    } else if (isBottomLeft) {
      path.moveTo(0, size.height / 2);
      path.lineTo(0, size.height);
      path.lineTo(size.width / 2, size.height);
    } else if (isBottomRight) {
      path.moveTo(size.width / 2, size.height);
      path.lineTo(size.width, size.height);
      path.lineTo(size.width, size.height / 2);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Custom clipper for creating a cutout in the overlay
class CutoutClipper extends CustomClipper<Path> {
  final double cutoutWidth;
  final double cutoutHeight;
  final double borderRadius;

  CutoutClipper({
    required this.cutoutWidth,
    required this.cutoutHeight,
    this.borderRadius = 0,
  });

  @override
  Path getClip(Size size) {
    // Calculate the position of the cutout
    final cutoutLeft = (size.width - cutoutWidth) / 2;
    final cutoutTop = (size.height - cutoutHeight) / 2;
    final cutoutRight = cutoutLeft + cutoutWidth;
    final cutoutBottom = cutoutTop + cutoutHeight;

    final path = Path()..addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    if (borderRadius > 0) {
      // Create a rounded rectangle cutout
      path.addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTRB(cutoutLeft, cutoutTop, cutoutRight, cutoutBottom),
          Radius.circular(borderRadius),
        ),
      );
    } else {
      // Create a rectangle cutout
      path.addRect(
        Rect.fromLTRB(cutoutLeft, cutoutTop, cutoutRight, cutoutBottom),
      );
    }

    // Use even-odd fill type to create a cutout
    return path..fillType = PathFillType.evenOdd;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
}
