import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pd_radio_button.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class FormIdentification extends StatelessWidget {
  final RecruitmentFormController controller;

  const FormIdentification({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _ktpSection(context),
        SizedBox(height: paddingMedium),
        _addressKtpSection(context),
        SizedBox(height: paddingMedium),
        _addressKtpSection(context, isHomeAddress: true),
      ],
    );
  }

  Column _addressKtpSection(BuildContext context, {bool? isHomeAddress}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(
          title:
              isHomeAddress == true
                  ? 'Alamat Tempat Tinggal Saat Ini'
                  : 'Alamat Sesuai KTP',
        ),
        if (isHomeAddress == true)
          Container(
            padding: EdgeInsets.symmetric(vertical: paddingSmall),
            child: Column(
              children: [
                PdlRadioButton(
                  index: 0,
                  selectedIndex: 1,
                  onTap: () {},
                  title: 'Sama dengan KTP',
                ),
                SizedBox(height: paddingSmall),
                PdlRadioButton(
                  index: 1,
                  selectedIndex: 1,
                  onTap: () {},
                  title: 'Beda dengan KTP',
                ),
              ],
            ),
          ),
        Text(
          isHomeAddress == true
              ? 'Masukan alama tempat tinggal saat ini'
              : 'pastikan data berikut sama persis dengan KTP.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            height: 2,
          ),
        ),
        _padding(
          PdlDropDown(
            item: ['Jakarta', 'Jawa Barat'],
            selectedItem:
                isHomeAddress == true
                    ? (controller.provinsiDomisiliController.text.isEmpty
                        ? 'Jakarta'
                        : controller.provinsiDomisiliController.text)
                    : (controller.provinsiKtpController.text.isEmpty
                        ? 'Jakarta'
                        : controller.provinsiKtpController.text),
            title: 'Provinsi',
            onChanged:
                (val) =>
                    isHomeAddress == true
                        ? controller.provinsiDomisiliController.text = val ?? ''
                        : controller.provinsiKtpController.text = val ?? '',
            enabled: true,
          ),
        ),
        _padding(
          PdlDropDown(
            item: ['Bandung', 'London'],
            selectedItem:
                isHomeAddress == true
                    ? (controller.kabupatenDomisiliController.text.isEmpty
                        ? 'Bandung'
                        : controller.kabupatenDomisiliController.text)
                    : (controller.kabupatenKtpController.text.isEmpty
                        ? 'Bandung'
                        : controller.kabupatenKtpController.text),
            title: 'Kabupaten / Kota',
            onChanged:
                (val) =>
                    isHomeAddress == true
                        ? controller.kabupatenDomisiliController.text =
                            val ?? ''
                        : controller.kabupatenKtpController.text = val ?? '',
            enabled: true,
          ),
        ),
        _padding(
          PdlDropDown(
            item: ['Mampang', 'London'],
            selectedItem:
                isHomeAddress == true
                    ? (controller.kelurahanDomisiliController.text.isEmpty
                        ? 'Mampang'
                        : controller.kelurahanDomisiliController.text)
                    : (controller.kelurahanKtpController.text.isEmpty
                        ? 'Mampang'
                        : controller.kelurahanKtpController.text),
            title: 'Kelurahan / Desa',
            onChanged:
                (val) =>
                    isHomeAddress == true
                        ? controller.kelurahanDomisiliController.text =
                            val ?? ''
                        : controller.kelurahanKtpController.text = val ?? '',
            enabled: true,
          ),
        ),
        _padding(
          PdlDropDown(
            item: ['Mampang', 'London'],
            selectedItem:
                isHomeAddress == true
                    ? (controller.kecamatanDomisiliController.text.isEmpty
                        ? 'Mampang'
                        : controller.kecamatanDomisiliController.text)
                    : (controller.kecamatanKtpController.text.isEmpty
                        ? 'Mampang'
                        : controller.kecamatanKtpController.text),
            title: 'Kecamatan',
            onChanged:
                (val) =>
                    isHomeAddress == true
                        ? controller.kecamatanDomisiliController.text =
                            val ?? ''
                        : controller.kecamatanKtpController.text = val ?? '',
            enabled: true,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Alamat',
            textController:
                isHomeAddress == true
                    ? controller.alamatDomisiliController
                    : controller.alamatKtpController,
          ),
        ),
        _padding(
          Row(
            children: [
              Expanded(
                child: PdlTextField(
                  label: 'RT',
                  textController:
                      isHomeAddress == true
                          ? controller.rtDomisiliController
                          : controller.rtKtpController,
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: PdlTextField(
                  label: 'RW',
                  textController:
                      isHomeAddress == true
                          ? controller.rwDomisiliController
                          : controller.rwKtpController,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Column _ktpSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: 'Data Diri Sesuai KTP'),
        Text(
          'Mohon mengisi data berikut sama persis dengan KTP.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            height: 2,
          ),
        ),
        _padding(
          PdlTextField(label: 'NIK', textController: controller.nikController),
        ),
        _padding(
          PdlTextField(
            label: 'Nama Lengkap Sesuai KTP',
            textController: controller.namaKtpController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Tempat Lahir',
            textController: controller.tempatLahirController,
          ),
        ),
        _padding(
          Row(
            children: [
              Expanded(
                child: PdlTextField(
                  label: 'Tanggal',
                  textController: controller.tanggalLahirController,
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: PdlDropDown(
                  item: controller.month,
                  selectedItem:
                      controller.bulanLahirController.text.isEmpty
                          ? 'Jan'
                          : controller.bulanLahirController.text,
                  title: 'Bulan',
                  disableSearch: true,
                  onChanged:
                      (val) => controller.bulanLahirController.text = val ?? '',
                  enabled: true,
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: PdlTextField(
                  label: 'Tahun',
                  textController: controller.tahunLahirController,
                ),
              ),
            ],
          ),
        ),
        _padding(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Jenis Kelamin',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(height: paddingSmall),
              Wrap(
                spacing: paddingMedium,
                children: [
                  PdlRadioButton(
                    index: 0,
                    selectedIndex: 1,
                    title: 'Laki-laki',
                    onTap: () {},
                  ),
                  PdlRadioButton(
                    index: 1,
                    selectedIndex: 1,
                    title: 'Perempuan',
                    onTap: () {},
                  ),
                ],
              ),
            ],
          ),
        ),
        _padding(
          PdlDropDown(
            item: ['Kawin', 'Belum Kawin'],
            selectedItem: 'Belum Kawin',
            title: 'Status Perkawinan',
            onChanged: (val) {},
            disableSearch: true,
            enabled: true,
          ),
        ),
      ],
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }
}
