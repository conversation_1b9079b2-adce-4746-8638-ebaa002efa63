import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class FormSelfIdentification extends StatelessWidget {
  final RecruitmentFormController controller;

  const FormSelfIdentification({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: 'Kelengkapan Data Pribadi'),
        Text(
          'Mohon mengisi data dengan benar, tanpa kesalahan.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            height: 2,
          ),
        ),
        SizedBox(height: paddingMedium),
        _infoContact(context),
        SizedBox(height: paddingMedium),
        _emergencyContact(context),
        SizedBox(height: paddingMedium),
        _bankSection(context),
        SizedBox(height: paddingMedium),
      ],
    );
  }

  Column _infoContact(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informasi & Kontak',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
        _padding(
          PdlTextField(
            label: 'Email',
            textController: controller.emailController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Nomor HP',
            isPhoneNumber: true,
            textController: controller.nomorHpController,
          ),
        ),
      ],
    );
  }

  Column _emergencyContact(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Kontak Darurat',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
        _padding(
          PdlTextField(
            label: 'Nama',
            textController: controller.emergencyNamaController,
          ),
        ),
        _padding(
          PdlDropDown(
            item: ['Istri', 'Suami'],
            selectedItem:
                controller.emergencyHubunganController.text.isEmpty
                    ? 'Istri'
                    : controller.emergencyHubunganController.text,
            title: 'Hubungan Dengan Anda',
            enabled: true,
            disableSearch: true,
            onChanged:
                (val) =>
                    controller.emergencyHubunganController.text = val ?? '',
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Nomor HP',
            isPhoneNumber: true,
            textController: controller.emergencyNomorHpController,
          ),
        ),
      ],
    );
  }

  Column _bankSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Nomor Rekening',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
        _padding(
          PdlTextField(
            label: 'Nama Pemilik Rekening',
            textController: controller.namaPemilikRekeningController,
          ),
        ),
        _padding(
          PdlTextField(
            label: 'Nomor Rekening',
            textController: controller.nomorRekeningController,
          ),
        ),
        _padding(
          PdlDropDown(
            item: ['BCA', 'Mandiri'],
            selectedItem:
                controller.namaPemilikRekeningController.text.isEmpty
                    ? 'BCA'
                    : controller.namaPemilikRekeningController.text,
            title: 'Bank',
            enabled: true,
            onChanged:
                (val) =>
                    controller.namaPemilikRekeningController.text = val ?? '',
          ),
        ),
      ],
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }
}
