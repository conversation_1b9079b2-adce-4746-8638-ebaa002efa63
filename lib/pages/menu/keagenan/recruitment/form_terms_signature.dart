import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class FormTermsSignature extends StatelessWidget {
  final RecruitmentFormController controller;

  const FormTermsSignature({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: '<PERSON><PERSON>'),
        _padding(
          Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              border: Border.all(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              ),
              borderRadius: BorderRadius.circular(radiusSmall),
            ),
            child: Row(
              children: [
                Icon(Icons.check_circle_outline, color: kColorGlobalGreen),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Text(
                    'Perjanjian <PERSON>agenan Asuransi Jiwa (PKAJ)',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                SizedBox(width: paddingSmall),
                Icon(Icons.chevron_right),
              ],
            ),
          ),
        ),

        _padding(
          Text(
            'Paraf dan Tanda Tangan',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
        ),
        // bellow this is condition when not all TNC are checked
        _padding(
          Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              color: kColorGlobalBgRed,
              borderRadius: BorderRadius.circular(radiusSmall),
            ),
            child: Row(
              children: [
                Icon(Icons.warning_amber_rounded, color: kColorGlobalRed),
                Expanded(
                  child: Text(
                    'Mohon membaca seluruh dokumen diatas sebelum menandatangani dokumen',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: kColorGlobalRed),
                  ),
                ),
              ],
            ),
          ),
        ),
        // Paraf & ttd muncul ketika all the TNC are checked
        _padding(
          SizedBox(
            width: Get.width,
            child: Row(
              children: [
                _infoCard(context),
                SizedBox(width: paddingMedium),
                _infoCard(context),
              ],
            ),
          ),
        ),
        _padding(
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.check_box_outline_blank_rounded,
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              ),
              SizedBox(width: paddingSmall),
              Expanded(
                child: Text(
                  'Dengan menandatangani dokumen berikut, saya (kandidat) telah membaca dan menyetujui seluruh dokumen perjanjian keagenan ini.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color:
                        Get.isDarkMode
                            ? kColorTextTersier
                            : kColorTextTersierLight,
                  ),
                ),
              ),
            ],
          ),
        ),
        _padding(Divider()),
        _padding(
          Text(
            'Anda dapat membagikan halaman ini untuk ditandatangani secara mandiri oleh kandidat.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            ),
          ),
        ),
        _padding(
          SizedBox(
            width: Get.width,
            child: PdlButton(title: 'Bagikan', onPressed: () {}),
          ),
        ),
      ],
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }

  Expanded _infoCard(BuildContext context) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(paddingMedium),
        decoration: BoxDecoration(
          color: kColorGlobalBgBlue,
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Text(
                  'Paraf',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: kColorPaninBlue,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingSmall),
            Container(
              width: Get.width,
              padding: EdgeInsets.all(paddingMedium),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(radiusMedium),
              ),
              child: AspectRatio(aspectRatio: 1),
            ),
          ],
        ),
      ),
    );
  }
}
