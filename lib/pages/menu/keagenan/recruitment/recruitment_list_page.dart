import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/recruitment_card.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_list_controller.dart';

import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RecruitmentListPage extends StatefulWidget {
  const RecruitmentListPage({super.key});

  @override
  State<RecruitmentListPage> createState() => _RecruitmentListPageState();
}

class _RecruitmentListPageState extends State<RecruitmentListPage>
    with WidgetsBindingObserver {
  final RecruitmentListController controller = Get.put(
    RecruitmentListController(),
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // Refresh data when app comes back to foreground
      controller.refreshData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Rekrut',
      controller: controller,
      onRefresh: () => controller.refreshData(),
      bottomAction:
          () => PdlBottomSheet(
            content: _methodBottomSheet(context),
            title: 'Pilih Metode',
          ),
      bottomText: 'Mulai Rekrut',
      child: Padding(
        padding: const EdgeInsets.all(paddingMedium),
        child: Column(
          children: [
            TitleWidget(title: 'Daftar Kandidat'),
            SizedBox(height: paddingMedium),
            SizedBox(
              width: Get.width,
              child: PdlTextField(
                hint: 'Cari nama kandidat',
                onChanged: (value) {
                  controller.updateSearchQuery(value);
                },
                prefixIcon: Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child: Utils.cachedSvgWrapper(
                    'icon/ic-linear-search -2.svg',
                    color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                  ),
                ),
              ),
            ),
            SizedBox(height: paddingMedium),

            // Tampilkan status loading
            Obx(() {
              if (controller.isLoadingForms.value) {
                return Center(
                  child: Padding(
                    padding: EdgeInsets.all(paddingMedium),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              // Tampilkan daftar form draft
              final forms = controller.filteredForms;

              if (forms.isEmpty) {
                return Center(
                  child: Padding(
                    padding: EdgeInsets.all(paddingMedium),
                    child: Column(
                      children: [
                        Icon(
                          Icons.note_alt_outlined,
                          size: 48,
                          color:
                              Get.isDarkMode
                                  ? kColorTextTersier
                                  : kColorTextTersierLight,
                        ),
                        SizedBox(height: paddingSmall),
                        Text(
                          'Belum ada form draft',
                          style: Get.textTheme.bodyMedium?.copyWith(
                            color:
                                Get.isDarkMode
                                    ? kColorTextTersier
                                    : kColorTextTersierLight,
                          ),
                        ),
                        SizedBox(height: paddingSmall),
                        Text(
                          'Mulai rekrut untuk membuat form baru',
                          style: Get.textTheme.bodySmall?.copyWith(
                            color:
                                Get.isDarkMode
                                    ? kColorTextTersier
                                    : kColorTextTersierLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              return Column(
                children:
                    forms.map((form) {
                      return RecruitmentCard(
                        form: form,
                        onTap: () => controller.continueForm(form.id!),
                      );
                    }).toList(),
              );
            }),
          ],
        ),
      ),
    );
  }

  SizedBox _methodBottomSheet(context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          SizedBox(height: paddingMedium),
          _methodBottomSheetCard(
            context,
            iconUrl: 'icon/ic-dual-note-dualtone.svg',
            title: 'Isi Form',
            description:
                'Bantu kandidat melengkapi formulir melalui device Anda',
            ontap: () {
              Get.back();
              controller.createNewForm();
            },
          ),
          SizedBox(height: paddingMedium),
          _methodBottomSheetCard(
            context,
            iconUrl: 'icon/ic-dual-qr-code.svg',
            title: 'Kirim QR Code',
            description:
                'Kandidat dapat melengkapi formulir secara mandiri melalui tautan yang Anda kirimkan',
            ontap: () {
              Get.back();
              PdlBottomSheet(
                content: _levelBottomSheet(context),
                title: 'Level Keagenan Kandidat',
              );
            },
          ),
          SizedBox(height: paddingMedium),
        ],
      ),
    );
  }

  Widget _methodBottomSheetCard(
    context, {
    required String title,
    required String description,
    required String iconUrl,
    required Function() ontap,
  }) {
    return GestureDetector(
      onTap: ontap,
      child: Container(
        color: Colors.transparent,
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                color: kColorGlobalBgBlue,
                shape: BoxShape.circle,
              ),
              width: 50,
              height: 50,
              padding: EdgeInsets.all(12),
              child: Utils.cachedSvgWrapper(iconUrl, color: kColorGlobalBlue),
            ),
            SizedBox(width: paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  SizedBox(height: paddingSmall),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  SizedBox _levelBottomSheet(context) {
    final dropdownDecoration = CustomDropdownDecoration(
      closedBorderRadius: BorderRadius.circular(8),
      expandedBorderRadius: BorderRadius.circular(8),
      closedFillColor: Theme.of(context).colorScheme.surface,
      expandedFillColor: Theme.of(context).colorScheme.surface,
      closedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      expandedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      listItemStyle: Theme.of(context).textTheme.bodyMedium,
      hintStyle: Theme.of(context).textTheme.bodyMedium,
      headerStyle: Theme.of(context).textTheme.bodyMedium,
    );
    final dropdownTheme = Theme.of(context).copyWith(
      inputDecorationTheme: const InputDecorationTheme(
        errorBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        focusedErrorBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
      ),
    );
    return SizedBox(
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: paddingMedium),
          Text('Level', style: Theme.of(context).textTheme.bodyMedium),
          SizedBox(height: paddingSmall),
          Theme(
            data: dropdownTheme,
            child: CustomDropdown(
              items: const ['BP', 'BM', 'BD'],
              initialItem: 'BP',
              onChanged: (val) {},
              closedHeaderPadding: EdgeInsets.all(12),
              decoration: dropdownDecoration,
            ),
          ),

          SizedBox(height: paddingMedium),
          PdlDropDown(
            title: 'KCU',
            item: ['GA Victory', 'GA Integrity', 'GA Valuable'],
            selectedItem: 'GA Victory',
            enabled: true,
            borderColor: null,

            onChanged: (val) => {},
          ),
          SizedBox(height: paddingMedium),
          SizedBox(
            width: Get.width,
            child: PdlButton(title: 'Lihat QR', onPressed: () {}),
          ),
          SizedBox(height: paddingMedium),
        ],
      ),
    );
  }
}
