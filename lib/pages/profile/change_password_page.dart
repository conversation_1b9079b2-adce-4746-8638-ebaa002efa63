import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/profile/change_password_controllers.dart';
import 'package:pdl_superapp/utils/common_widgets.dart';
import 'package:pdl_superapp/utils/constants.dart';

class ChangePasswordPage extends StatelessWidget {
  ChangePasswordPage({super.key});

  final ChangePasswordControllers controller = Get.put(
    ChangePasswordControllers(),
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () {},
      backEnabled: true,
      controller: controller,
      title: 'title_create_password'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: paddingLarge),
            TitleWidget(title: 'title_create_password'.tr),
            SizedBox(height: paddingSmall),
            Text(
              'sub_title_create_password'.tr,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: paddingMedium),
            _textFieldWrapper(
              PdlTextField(
                label: 'label_old_password'.tr,
                hint: 'hint_old_password'.tr,
                textController: controller.oldPasswordTextController,
                onChanged: (val) {
                  controller.emptValidator();
                },
                isPassword: true,
              ),
            ),
            Obx(
              () => _textFieldWrapper(
                PdlTextField(
                  label: 'label_new_password'.tr,
                  hint: 'hint_new_password'.tr,
                  textController: controller.newPasswordTextController,
                  borderColor:
                      controller.isConfirmSame.isTrue ? null : kColorError,
                  onChanged: (val) {
                    controller.passwordCombinationValidator(val);
                    controller.emptValidator();
                  },
                  isPassword: true,
                ),
              ),
            ),
            Obx(
              () => _textFieldWrapper(
                PdlTextField(
                  label: 'label_confirm_password'.tr,
                  hint: 'label_confirm_password'.tr,
                  textController: controller.confirmPasswordTextController,
                  borderColor:
                      controller.isConfirmSame.isTrue ? null : kColorError,
                  isPassword: true,
                  onChanged: (val) {
                    controller.onChangeConfirm(val);
                    controller.emptValidator();
                  },
                ),
              ),
            ),
            Text(
              'text_password_tnc'.tr,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: kColorTextTersier),
            ),
            SizedBox(height: paddingExtraSmall),
            Obx(
              () => _tncPassword(
                context,
                title: 'text_password_tnc_length'.tr,
                colors:
                    controller.is8Char.isTrue
                        ? Theme.of(context).colorScheme.primary
                        : kColorTextTersier,
                icons: Icon(
                  controller.is8Char.isTrue ? Icons.check : Icons.circle,
                  size: controller.is8Char.isTrue ? 14 : paddingSmall,
                  color:
                      controller.is8Char.isTrue
                          ? Theme.of(context).colorScheme.primary
                          : kColorTextTersier,
                ),
              ),
            ),
            SizedBox(height: paddingExtraSmall),
            Obx(
              () => _tncPassword(
                context,
                title: 'text_password_tnc_validator'.tr,
                colors:
                    controller.isValidChar.isTrue
                        ? Theme.of(context).colorScheme.primary
                        : kColorTextTersier,
                icons: Icon(
                  controller.isValidChar.isTrue ? Icons.check : Icons.circle,
                  size: controller.isValidChar.isTrue ? 14 : paddingSmall,
                  color:
                      controller.isValidChar.isTrue
                          ? Theme.of(context).colorScheme.primary
                          : kColorTextTersier,
                ),
              ),
            ),

            Obx(() {
              if (controller.isConfirmSame.isTrue) {
                return Container();
              }
              return CommonWidgets.errorCard(
                context,
                content: 'error_label_not_same'.tr,
              );
            }),
            Container(
              padding: EdgeInsets.only(top: paddingSmall),
              width: Get.width,
              child: Obx(
                () => PdlButton(
                  controller: controller,
                  onPressed:
                      controller.isSaveAvailable.isTrue
                          ? () => controller.performSave()
                          : null,
                  title: 'button_save'.tr,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Padding _textFieldWrapper(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingMedium),
      child: child,
    );
  }

  RichText _tncPassword(
    BuildContext context, {
    required String title,
    required Color colors,
    required Icon icons,
  }) {
    return RichText(
      text: TextSpan(
        children: [
          WidgetSpan(
            child: Container(
              width: 20,
              alignment: Alignment.center,
              child: icons,
            ),
            alignment: PlaceholderAlignment.middle,
          ),
          TextSpan(
            text: title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: colors),
          ),
        ],
      ),
    );
  }
}
