import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/profile/profile_edit_controller.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ProfileEditPage extends StatelessWidget {
  ProfileEditPage({super.key});

  final ProfileEditController controller = Get.put(ProfileEditController());

  @override
  Widget build(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    if (Get.arguments != null) {
      UserModels userDatas = Get.arguments as UserModels;
      controller.userData.value = userDatas;
      controller.setInitialData(userDatas);
    }
    return Obx(
      () => BaseDetailPage(
        backEnabled: true,
        controller: controller,
        onRefresh: () {},
        title: 'label_my_profile'.tr,
        bottomAction:
            controller.isEdit.isTrue
                ? () => controller.checkAttachement(context)
                : null,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: paddingLarge),
              Obx(
                () => TitleWidget(
                  title: 'label_my_profile'.tr,
                  action: Obx(
                    () =>
                        controller.isEdit.isTrue
                            ? Container()
                            : GestureDetector(
                              onTap: () => controller.isEdit.value = true,
                              child: Wrap(
                                crossAxisAlignment: WrapCrossAlignment.center,
                                children: [
                                  Text(
                                    'sub_title_edit'.tr,
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium?.copyWith(
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                  SizedBox(width: paddingSmall),
                                  Utils.cachedSvgWrapper(
                                    'icon/ic-linear-pen-new-square.svg',
                                    color: Theme.of(context).primaryColor,
                                    height: 15,
                                  ),
                                ],
                              ),
                            ),
                  ),
                ),
              ),
              Container(
                width: Get.width,
                padding: EdgeInsets.all(paddingLarge),
                child: Center(
                  child: Stack(
                    children: [
                      Container(
                        width: 90,
                        height: 90,
                        clipBehavior: Clip.hardEdge,
                        decoration: BoxDecoration(
                          color: kColorGlobalBgGreen,
                          borderRadius: BorderRadius.circular(90),
                        ),
                        child: Obx(
                          () =>
                              controller.currentProfilePicture.value != ''
                                  ? CachedNetworkImage(
                                    imageUrl:
                                        controller.currentProfilePicture.value,
                                    fit: BoxFit.cover,
                                    alignment: Alignment.center,
                                  )
                                  : controller.userData.value.photo != null
                                  ? CachedNetworkImage(
                                    imageUrl: controller.userData.value.photo!,
                                    fit: BoxFit.cover,
                                    alignment: Alignment.center,
                                  )
                                  : Center(
                                    child: Text(
                                      Utils.getInitials(
                                        controller.userData.value.agentName ??
                                            '-',
                                      ),
                                      style: Theme.of(
                                        context,
                                      ).textTheme.headlineLarge?.copyWith(
                                        color: kColorTextTersierLight,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ),
                        ),
                      ),
                      if (controller.isEdit.isTrue)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: () => controller.profileBottomSheet(context),
                            child: Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface,
                                borderRadius: BorderRadius.circular(32),
                                border: Border.all(
                                  color:
                                      Get.isDarkMode
                                          ? kColorBorderDark
                                          : kColorBorderLight,
                                ),
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(6),
                                child: Utils.cachedSvgWrapper(
                                  'icon/ic-linear-pen-new-square.svg',
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: paddingSmall),
              isWideScreen
                  ? _webForm(context, userData: controller.userData.value)
                  : _mobileForm(context, userData: controller.userData.value),
            ],
          ),
        ),
      ),
    );
  }

  Widget _webForm(context, {required UserModels userData}) {
    bool isBpBmBdRole = [kLevelBP, kLevelBM, kLevelBD].contains(userData.level);

    return Column(
      children: [
        if (isBpBmBdRole) ...[
          // Fields for BP, BM, BD roles
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'label_form_full_name'.tr,
              intialValue: userData.agentName,
              textController: controller.nameTextController,
              fieldName: kFieldagentName,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'label_form_email'.tr,
              intialValue: userData.email,
              textController: controller.emailTextController,
              fieldName: kFieldemail,
            ),
          ),
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'label_form_phone'.tr,
              intialValue: userData.phoneNumber,
              textController: controller.phoneTextController,
              fieldName: kFieldphoneNumber,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'label_form_agent_code'.tr,
              intialValue: userData.agentCode,
              isRestricted: true,
              textController: controller.agentCodeTextController,
              fieldName: kFieldagentCode,
            ),
          ),
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'label_form_branch_code'.tr,
              intialValue: userData.branchCode,
              isRestricted: true,
              textController: controller.branchCodeTextController,
              fieldName: kFieldbranchCode,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'label_form_level'.tr,
              intialValue: userData.level,
              isRestricted: true,
              textController: controller.levelTextController,
              fieldName: kFieldlevel,
            ),
          ),
          _webFormWrapper(
            leftContent: _dropDownWrapper(
              context,
              userData: userData,
              label: 'label_form_bank'.tr,
              item: controller.listBank,
              selectedItem: controller.setSelectedItem(
                controller.listBank,
                userData.bank ?? '',
              ),
              enabled: controller.isEdit.isTrue,
              onChanged: (val) {
                controller.bankTextController.text = val;
              },
              fieldName: kFieldbank,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'label_form_bank_number'.tr,
              intialValue: userData.bankAccountNumber,
              textController: controller.bankNumberTextController,
              fieldName: kFieldbankAccountNumber,
            ),
          ),
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'label_form_address'.tr,
              intialValue: userData.address,
              isTextArea: true,
              textController: controller.addressTextController,
              fieldName: kFieldaddress,
            ),
            rightContent: _dropDownWrapper(
              context,
              userData: userData,
              label: 'label_form_marital'.tr,
              item: controller.listStatus,
              selectedItem: controller.setSelectedItem(
                controller.listStatus,
                userData.maritalStatus ?? '',
              ),
              enabled: controller.isEdit.isTrue,
              onChanged: (val) {
                controller.maritalTextController.text = val;
              },
              fieldName: kFieldmaritalStatus,
            ),
          ),
          _webFormWrapper(
            leftContent: _dropDownWrapper(
              context,
              fieldName: kFieldeducation,
              userData: userData,
              label: 'label_form_education'.tr,
              item: controller.listPendidikan,
              selectedItem: controller.setSelectedItem(
                controller.listPendidikan,
                userData.education ?? '',
              ),
              enabled: controller.isEdit.isTrue,
              onChanged: (val) {
                controller.educationTextController.text = val;
              },
            ),
            rightContent: Container(),
          ),
        ] else ...[
          // Fields for other roles
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'Nama lengkap',
              intialValue: userData.name,
              textController: controller.fullNameTextController,
              fieldName: 'name',
              isRestricted: true,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'Username',
              intialValue: userData.username,
              textController: controller.usernameTextController,
              fieldName: 'username',
              isRestricted: true,
            ),
          ),
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'Channel',
              intialValue: userData.channel,
              textController: controller.channelTextController,
              fieldName: 'channel',
              isRestricted: true,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'Jabatan',
              intialValue: userData.roles?.name,
              textController: controller.roleNameTextController,
              fieldName: 'roles.name',
              isRestricted: true,
            ),
          ),
          _webFormWrapper(
            leftContent: _textFieldWrapper(
              context,
              label: 'KCU / KCP / Cabang',
              intialValue: '',
              textController: controller.branchNameTextController,
              fieldName: 'branchName',
              isRestricted: true,
            ),
            rightContent: _textFieldWrapper(
              context,
              label: 'Status',
              intialValue: 'aktif',
              textController: controller.statusTextController,
              fieldName: 'status',
              isRestricted: true,
            ),
          ),
        ],
      ],
    );
  }

  Row _webFormWrapper({
    required Widget leftContent,
    required Widget rightContent,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(child: leftContent),
        SizedBox(width: paddingMedium),
        Expanded(child: rightContent),
      ],
    );
  }

  Widget _mobileForm(context, {required UserModels userData}) {
    bool isBpBmBdRole = [kLevelBP, kLevelBM, kLevelBD].contains(userData.level);

    return Column(
      children: [
        if (isBpBmBdRole) ...[
          // Fields for BP, BM, BD roles
          _textFieldWrapper(
            context,
            label: 'label_form_full_name'.tr,
            intialValue: userData.agentName,
            textController: controller.nameTextController,
            fieldName: kFieldagentName,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_email'.tr,
            intialValue: userData.email,
            textController: controller.emailTextController,
            fieldName: kFieldemail,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_phone'.tr,
            intialValue: userData.phoneNumber,
            textController: controller.phoneTextController,
            fieldName: kFieldphoneNumber,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_agent_code'.tr,
            intialValue: userData.agentCode,
            isRestricted: true,
            textController: controller.agentCodeTextController,
            fieldName: kFieldagentCode,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_branch_code'.tr,
            intialValue: userData.branchCode,
            isRestricted: true,
            textController: controller.branchCodeTextController,
            fieldName: kFieldbranchCode,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_level'.tr,
            intialValue: userData.level,
            isRestricted: true,
            textController: controller.levelTextController,
            fieldName: kFieldlevel,
          ),
          _dropDownWrapper(
            context,
            userData: userData,
            label: 'label_form_bank'.tr,
            item: controller.listBank,
            selectedItem: controller.setSelectedItem(
              controller.listBank,
              userData.bank ?? '',
            ),
            enabled: controller.isEdit.isTrue,
            onChanged: (val) {
              controller.bankTextController.text = val;
            },
            fieldName: kFieldbank,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_bank_number'.tr,
            intialValue: userData.bankAccountNumber,
            textController: controller.bankNumberTextController,
            fieldName: kFieldbankAccountNumber,
          ),
          _textFieldWrapper(
            context,
            label: 'label_form_address'.tr,
            intialValue: userData.address,
            isTextArea: true,
            textController: controller.addressTextController,
            fieldName: kFieldaddress,
          ),
          _dropDownWrapper(
            context,
            userData: userData,
            label: 'label_form_marital'.tr,
            item: controller.listStatus,
            selectedItem: controller.setSelectedItem(
              controller.listStatus,
              userData.maritalStatus ?? '',
            ),
            enabled: controller.isEdit.isTrue,
            onChanged: (val) {
              controller.maritalTextController.text = val;
            },
            fieldName: kFieldmaritalStatus,
          ),
          _dropDownWrapper(
            context,
            fieldName: kFieldeducation,
            userData: userData,
            label: 'label_form_education'.tr,
            item: controller.listPendidikan,
            selectedItem: controller.setSelectedItem(
              controller.listPendidikan,
              userData.education ?? '',
            ),
            enabled: controller.isEdit.isTrue,
            onChanged: (val) {
              controller.educationTextController.text = val;
            },
          ),
        ] else ...[
          // Fields for other roles
          _textFieldWrapper(
            context,
            label: 'Nama lengkap',
            intialValue: userData.name,
            textController: controller.fullNameTextController,
            fieldName: 'name',
            isRestricted: true,
          ),
          _textFieldWrapper(
            context,
            label: 'Username',
            intialValue: userData.username,
            textController: controller.usernameTextController,
            fieldName: 'username',
            isRestricted: true,
          ),
          _textFieldWrapper(
            context,
            label: 'Channel',
            intialValue: userData.channel,
            textController: controller.channelTextController,
            fieldName: 'channel',
            isRestricted: true,
          ),
          _textFieldWrapper(
            context,
            label: 'Jabatan',
            intialValue: userData.roles?.name,
            textController: controller.roleNameTextController,
            fieldName: 'roles.name',
            isRestricted: true,
          ),
          _textFieldWrapper(
            context,
            label: 'KCU / KCP / Cabang',
            intialValue: '',
            textController: controller.branchNameTextController,
            fieldName: 'branchName',
            isRestricted: true,
          ),
          _textFieldWrapper(
            context,
            label: 'Status',
            intialValue: 'aktif',
            textController: controller.statusTextController,
            fieldName: 'status',
            isRestricted: true,
          ),
        ],
        SizedBox(height: paddingLarge),
      ],
    );
  }

  Padding _dropDownWrapper(
    context, {
    required UserModels userData,
    required String label,
    required List<String> item,
    required String selectedItem,
    required bool enabled,
    required Function(String) onChanged,
    required String fieldName,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingMedium),
      child: Column(
        children: [
          PdlDropDown(
            title: label,
            item: item,
            selectedItem: selectedItem,
            enabled:
                controller.inApprovalList.contains(fieldName)
                    ? false
                    : controller.isEdit.value,
            borderColor:
                controller.inApprovalList.contains(fieldName)
                    ? kColorError
                    : null,
            onChanged: (val) => onChanged(val!),
          ),
          if (controller.inApprovalList.contains(fieldName))
            _changeInProgress(context),
        ],
      ),
    );
  }

  Padding _textFieldWrapper(
    context, {
    required String label,
    String? intialValue,
    required TextEditingController textController,
    bool? isRestricted,
    bool? isTextArea,
    required String fieldName,
  }) {
    if (isRestricted == true) {
      return Padding(
        padding: const EdgeInsets.only(bottom: paddingMedium),
        child: Column(
          children: [
            PdlTextField(
              label: label,
              textController: textController,
              hint: intialValue,
              enabled: false,
              isTextArea: isTextArea,
            ),
          ],
        ),
      );
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingMedium),
      child: Obx(
        () => Column(
          children: [
            PdlTextField(
              label: label,
              textController: textController,
              hint: intialValue,
              borderColor:
                  controller.inApprovalList.contains(fieldName)
                      ? kColorError
                      : null,
              enabled:
                  controller.inApprovalList.contains(fieldName)
                      ? false
                      : controller.isEdit.value,
              isTextArea: isTextArea,
            ),
            if (controller.inApprovalList.contains(fieldName))
              _changeInProgress(context),
          ],
        ),
      ),
    );
  }

  Container _changeInProgress(context) {
    return Container(
      width: Get.width,
      margin: EdgeInsets.only(top: paddingExtraSmall),
      padding: EdgeInsets.all(paddingSmall),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        'label_change_in_progress'.tr,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.error,
        ),
      ),
    );
  }
}
