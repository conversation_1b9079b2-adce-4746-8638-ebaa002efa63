import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/custom_switch.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/controllers/profile/profile_controller.dart';
import 'package:pdl_superapp/pages/sliver_wrapper.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/common_widgets.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:qr_flutter/qr_flutter.dart';

class ProfilePage extends StatelessWidget {
  ProfilePage({super.key});

  final ProfileController controller = Get.put(
    ProfileController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return isWideScreen
        ? RefreshIndicator(
          onRefresh: () async {
            controller.load();
            // Wait for loading to complete
            while (controller.isLoading.value) {
              await Future.delayed(Duration(milliseconds: 100));
            }
            return;
          },
          child: SingleChildScrollView(child: _body(context)),
        )
        : RefreshIndicator(
          onRefresh: () async {
            controller.load();
            // Wait for loading to complete
            while (controller.isLoading.value) {
              await Future.delayed(Duration(milliseconds: 100));
            }
            return;
          },
          child: SliverWrapper(
            collapseContent: _collapseContent(),
            expandContent: _expandContent(context),
            widget: [_body(context)],
          ),
        );
  }

  Container _body(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: SizedBox(
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isWideScreen) SizedBox(height: 100),
            if (isWideScreen) _wideScreenHeader(context),
            _info(context),
            _title(context, text: '${'label_lisence'.tr} (1/2)'),
            _license(context),
            SizedBox(
              width: Get.width,
              child: Wrap(
                alignment: WrapAlignment.spaceBetween,
                runAlignment: WrapAlignment.spaceBetween,
                children: [
                  _menuWrapper(
                    context,
                    childern: [
                      _title(context, text: 'title_account'.tr),
                      _account(context),
                    ],
                  ),
                  _menuWrapper(
                    context,
                    childern: [
                      _title(context, text: 'title_keagenan'.tr),
                      _keagenan(context),
                    ],
                  ),
                  _menuWrapper(
                    context,
                    childern: [
                      _title(context, text: 'label_application'.tr),
                      _application(context),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              width: Get.width,
              padding: EdgeInsets.symmetric(vertical: paddingLarge),
              child: Text(
                '${'label_version'.tr} 1.2.0',
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Padding _wideScreenHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: paddingSmall),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(70),
              color: kColorBorderDark,
            ),
          ),
          SizedBox(height: paddingSmall),
          Text(
            controller.userData.value.agentName ?? '',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              fontSize: 20,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
          SizedBox(height: paddingExtraSmall),
          Text(
            controller.userData.value.roleName ?? '',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _menuWrapper(BuildContext context, {required List<Widget> childern}) {
    final bool isWideScreen = kIsWeb;
    return SizedBox(
      width: isWideScreen ? Get.width / 5.3 : Get.width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: childern,
      ),
    );
  }

  Column _application(BuildContext context) => Column(
    children: [
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-Gear.svg',
        title: 'label_app_setting'.tr,
        onPressed: () => Get.toNamed(Routes.SETTING),
      ),
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-clipboard-linear.svg',
        title: 'label_privacy'.tr,
      ),
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-dialog.svg',
        title: 'label_qna'.tr,
        onPressed: () => Get.toNamed(Routes.QNA),
      ),
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-notebook.svg',
        title: 'label_tutorial'.tr,
      ),
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-logout.svg',
        title: 'button_logout'.tr,
        suffix: Container(),
        onPressed: () => controller.performLoggedOut(),
        color: kColorError,
      ),
    ],
  );

  Column _keagenan(BuildContext context) => Column(
    children: [
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-documents.svg',
        title: 'label_download_pdf'.tr,
        suffix: SizedBox(
          width: 40,
          child: Utils.cachedSvgWrapper(
            'icon/ic-linear-download.svg',
            color: Theme.of(context).primaryColor,
            height: 24,
            fit: BoxFit.contain,
          ),
        ),
        isLast: true,
      ),
    ],
  );

  Column _account(BuildContext context) => Column(
    children: [
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-user-rounded.svg',
        title: 'label_my_profile'.tr,
        onPressed:
            () => Get.toNamed(
              Routes.PROFILE_EDIT,
              arguments: controller.userData.value,
            ),
      ),
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-lock.svg',
        title: 'label_change_password'.tr,
        onPressed: () => Get.toNamed(Routes.CHANGE_PASSWORD),
      ),
      Obx(
        () => CommonWidgets.profileContentCard(
          context,
          iconUrl: 'icon/ic-linear-scan-user.svg',
          title: 'label_biometric'.tr,
          suffix: CustomSwitch(
            value: controller.isBioActive.value,
            onChanged: (val) => controller.setBiometric(),
          ),
          isLast: true,
        ),
      ),
    ],
  );

  Widget _info(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return Center(
      child: SizedBox(
        width: isWideScreen ? Get.width / 4 : Get.width,
        child: _cardWrapper(
          context,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Obx(
                      () => Text(
                        controller.userData.value.agentCode ?? '-',
                        style: Theme.of(context).textTheme.labelMedium
                            ?.copyWith(fontWeight: FontWeight.w700),
                      ),
                    ),
                    SizedBox(height: paddingExtraSmall),
                    Obx(
                      () => Text(
                        controller.userData.value.branchName ?? '-',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color:
                              Get.isDarkMode
                                  ? kColorTextTersier
                                  : kColorTextTersierLight,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap:
                    () => PdlBaseDialog(
                      context: context,
                      bgIsBlurred: true,
                      title: 'QR Code',
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 150,
                            height: 150,
                            padding: EdgeInsets.all(paddingSmall),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.secondary,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: QrImageView(
                              data: 'This is Data',
                              dataModuleStyle: QrDataModuleStyle(
                                dataModuleShape: QrDataModuleShape.square,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                              eyeStyle: QrEyeStyle(
                                eyeShape: QrEyeShape.square,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                              vertical: paddingLarge,
                            ),
                            child: Text(
                              'William Sachiko',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.only(top: paddingSmall),
                            width: Get.width,
                            child: Text(
                              'Pindai kode QR ini menggunakan smartphone calon kandidat atau bagikan melalui whatsapp dan media sosial lain untuk mempermudah anda dalam merekrut kandidat.',
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(
                            width: Get.width,
                            padding: EdgeInsets.only(top: paddingMedium),
                            child: FilledButton(
                              // onPressed: () => Get.toNamed(Routes.FIRST_LOGIN_SUCCESS),
                              onPressed: () {},
                              style: FilledButton.styleFrom(
                                disabledBackgroundColor:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorBorderLight,
                                disabledForegroundColor: Color(0xFF6D6D6D),
                              ),
                              child: Wrap(
                                alignment: WrapAlignment.center,
                                crossAxisAlignment: WrapCrossAlignment.center,
                                children: [
                                  Text('Bagikan'),
                                  SizedBox(width: paddingSmall),
                                  Utils.cachedSvgWrapper(
                                    'icon/ic-linear-share.svg',
                                    color: kColorBgLight,
                                    height: 20,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        Get.isDarkMode
                            ? Theme.of(context).colorScheme.surface
                            : Theme.of(context).colorScheme.secondary,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                    ),
                  ),
                  padding: EdgeInsets.all(paddingSmall),
                  child: Wrap(
                    spacing: paddingSmall,
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      Text(
                        'QR Kode',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Utils.cachedSvgWrapper(
                        'icon/ic-linear-qr.svg',
                        color: Theme.of(context).primaryColor,
                        width: 24,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Padding _title(BuildContext context, {required text}) {
    return Padding(
      padding: EdgeInsets.only(bottom: paddingMedium, top: paddingLarge),
      child: Text(
        text,
        style: Theme.of(
          context,
        ).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w700),
      ),
    );
  }

  SizedBox _license(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Wrap(
        alignment: WrapAlignment.spaceBetween,
        runAlignment: WrapAlignment.spaceBetween,
        runSpacing: paddingMedium,
        children: [
          _lisenceCard(
            context,
            isCompleted: true,
            title: 'AAJI',
            number: controller.userData.value.licenseNumberAAJI,
            expiredDate: controller.userData.value.licenseExpiredDateAAJI,
          ),
          _lisenceCard(
            context,
            isCompleted: true,
            title: 'AASI',
            number: controller.userData.value.licenseNumberAASI,
            expiredDate: controller.userData.value.licenseExpiredDateAASI,
          ),
          _lisenceCard(context, isCompleted: false, title: 'Others'),
        ],
      ),
    );
  }

  SizedBox _lisenceCard(
    BuildContext context, {
    required bool isCompleted,
    required String title,
    String? expiredDate,
    String? number,
  }) {
    final bool isWideScreen = kIsWeb;
    return SizedBox(
      width: isWideScreen ? Get.width / 3.45 : Get.width / 2.25,
      child: _cardWrapper(
        context,
        isCompleted: isCompleted,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w700),
            ),
            SizedBox(height: paddingSmall),
            if (isCompleted == true)
              Text(
                number ?? '-',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w700,
                  color:
                      Get.isDarkMode
                          ? kColorTextTersier
                          : kColorTextTersierLight,
                ),
              ),
            SizedBox(height: paddingSmall),
            if (isCompleted == true)
              Text(
                'Exp $expiredDate',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w400),
              ),

            if (isCompleted == false)
              Container(
                decoration: BoxDecoration(
                  color: kColorGlobalBgWarning,
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: EdgeInsets.symmetric(
                  vertical: paddingExtraSmall,
                  horizontal: paddingSmall,
                ),
                child: Text(
                  'Wajib diikuti',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: kColorGlobalWarning),
                ),
              ),
            SizedBox(height: paddingSmall),
            isCompleted == true
                ? Container(
                  margin: EdgeInsets.only(top: paddingExtraSmall),
                  decoration: BoxDecoration(
                    color: Color(0xFFF3FAF1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  padding: EdgeInsets.symmetric(
                    vertical: paddingSmall,
                    horizontal: paddingMedium,
                  ),
                  child: Text(
                    'Aktif',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Color(0xFF126F3C),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
                : SizedBox(
                  width: Get.width,
                  child: FilledButton(
                    // onPressed: () => Get.toNamed(Routes.FIRST_LOGIN_SUCCESS),
                    onPressed: () {},
                    child: Text('Daftar'),
                  ),
                ),
          ],
        ),
      ),
    );
  }

  Widget _cardWrapper(
    BuildContext context, {
    required Widget child,
    bool? isCompleted,
  }) {
    return Container(
      width: Get.width,

      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceTint,
        border: Border.all(
          color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          if (isCompleted != null)
            Positioned(
              right: 0,
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color:
                      isCompleted == true
                          ? kColorGlobalBgGreen
                          : kColorGlobalBgWarning,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(16),
                    topLeft: Radius.circular(30),
                    bottomLeft: Radius.circular(30),
                  ),
                ),
                child: Center(
                  child: Utils.cachedSvgWrapper(
                    isCompleted == true
                        ? 'icon/ic-bold-verified-check.svg'
                        : 'icon/ic-bold-Info.svg',
                    color:
                        isCompleted == true
                            ? kColorGlobalGreen
                            : kColorGlobalWarning,
                  ),
                ),
              ),
            ),
          Padding(padding: EdgeInsets.all(paddingMedium), child: child),
        ],
      ),
    );
  }

  Text _collapseContent() {
    return Text(
      "Profile",
      key: ValueKey("Collapsed"),
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Colors.white,
      ),
    );
  }

  Container _expandContent(BuildContext context) {
    RxString agentInitials = ''.obs;
    RxString agentName = '-'.obs;
    RxString agentLevel = '-'.obs;
    switch (controller.userLevel.value) {
      case kUserLevelBp:
      case kUserLevelBm:
      case kUserLevelBd:
        agentName.value = controller.userData.value.agentName ?? '-';
        agentLevel.value =
            '${controller.userData.value.level ?? '-'} - ${controller.userData.value.agentCode ?? '-'}';
        break;
      case kUserLevelCAO:
        agentName.value = 'CAO belum di set';
      default:
        // BDM up selain CAO
        agentName.value = controller.userData.value.name ?? '-';
        agentLevel.value = controller.userData.value.roles?.name ?? '-';
    }
    if (controller.userData.value.picture != null &&
        controller.userData.value.picture != '') {
      agentInitials.value = '';
    } else {
      agentInitials.value = Utils.getInitials(agentName.value);
    }
    return Container(
      width: Get.width,
      margin: EdgeInsets.only(bottom: 40),
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 70,
            height: 70,
            child: CircleAvatar(
              radius: 70,
              backgroundColor: Theme.of(context).colorScheme.primary,
              backgroundImage:
                  controller.userData.value.picture != null &&
                          controller.userData.value.picture != ''
                      ? CachedNetworkImageProvider(
                        controller.userData.value.picture!,
                      )
                      : null,
              child: Text(
                agentInitials.value,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ), // Replace with your image
            ),
          ),
          SizedBox(height: paddingSmall),
          Text(
            agentName.value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              fontSize: 20,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
          SizedBox(height: paddingExtraSmall),
          Text(
            agentLevel.value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
