import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/profile/qna_controller.dart';
import 'package:pdl_superapp/models/question_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class QnaPage extends StatelessWidget {
  QnaPage({super.key});

  final QnaController controller = Get.put(QnaController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'label_qna'.tr,
      controller: controller,
      onRefresh: () => controller.load(),
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(
          () => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: paddingLarge),
              TitleWidget(title: 'title_qna'.tr),
              SizedBox(height: paddingMedium),
              PdlTextField(
                prefixIcon: Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child: Utils.cachedSvgWrapper('icon/ic-linear-search -2.svg'),
                ),
                hint: 'Cari Pertanyaan',
                textController: controller.searchTextController,
                suffix: GestureDetector(
                  onTap: () {
                    controller.searchTextController.clear();
                  },
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(Icons.close, size: 18, color: kColorBgLight),
                  ),
                ),
                onEditingComplete: () {
                  controller.questionParams =
                      controller.searchTextController.text;
                  controller.load();
                },
              ),
              SizedBox(height: paddingLarge),
              if (controller.searchTextController.text.isNotEmpty &&
                  controller.arrData.isEmpty)
                SizedBox(
                  width: Get.width,
                  child: Column(
                    children: [
                      Utils.cachedSvgWrapper(
                        'icon/illustration-empty-no-found.svg',
                      ),
                      SizedBox(height: paddingSmall),
                      Text(
                        'subtitle_search_not_found'.tr,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color:
                              Get.isDarkMode
                                  ? kColorTextTersier
                                  : kColorTextTersierLight,
                        ),
                      ),
                    ],
                  ),
                ),
              for (int i = 0; i < controller.arrData.length; i++)
                _questionCard(
                  context,
                  questionData: controller.arrData[i],
                  index: i,
                ),
              SizedBox(height: paddingExtraLarge),
            ],
          ),
        ),
      ),
    );
  }

  _questionCard(
    context, {
    required QuestionModels questionData,
    required int index,
  }) {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: paddingSmall),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    questionData.question ?? '-',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                Icon(Icons.chevron_right_outlined),
              ],
            ),
          ),
          Divider(),
        ],
      ),
    );
  }
}
