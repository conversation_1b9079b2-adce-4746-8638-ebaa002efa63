import 'package:fleather/fleather.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/read_page_controller.dart';
import 'package:pdl_superapp/pages/authentication/first_login/base_first_login.dart';
import 'package:pdl_superapp/utils/constants.dart';

class ReadPage extends StatelessWidget {
  ReadPage({super.key});

  final ReadPageController controller = Get.put(ReadPageController());

  @override
  Widget build(BuildContext context) {
    var args = Get.arguments;
    String type = args['type'];
    controller.performGetInformation(type);

    return Obx(
      () => BaseFirstLogin(
        title: controller.infoData.value.title ?? '-',
        backEnabled: true,
        child: Padding(
          padding: EdgeInsets.all(paddingMedium),
          child:
              controller.fleatherReady.isTrue
                  ? FleatherEditor(
                    controller: controller.jsonController,
                    readOnly: true,
                    enableInteractiveSelection: false,
                  )
                  : Text('content'),
        ),
      ),
    );
  }
}
