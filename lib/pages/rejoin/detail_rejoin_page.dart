import 'dart:io';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_circle_avatar.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/status_label.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/rejoin/detail_rejoin_controller.dart';
import 'package:pdl_superapp/pages/rejoin/widget_detail_rejoin/alert_danger_rejoin_cancel.dart';
import 'package:pdl_superapp/pages/rejoin/widget_detail_rejoin/bottom_sheet_rejoin.dart';
import 'package:pdl_superapp/pages/rejoin/widget_detail_rejoin/history_approval_rejoin.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:path/path.dart' as path;

class DetailRejoinPage extends StatelessWidget {
  DetailRejoinPage({super.key});

  final DetailRejoinController controller = Get.put(DetailRejoinController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Formulir Bergabung Kembali',
      controller: controller,
      onRefresh: () {},
      bottomWidget: _bottomButton(context),
      child: Padding(
        padding: const EdgeInsets.all(paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TitleWidget(
              title: 'Formulir Bergabung Kembali',
              action: StatusLabel(status: Status.diproses),
            ),
            SizedBox(height: paddingLarge),
            AlertDangerRejoinCancel(),
            SizedBox(height: paddingLarge),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.grey),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: CachedNetworkImage(
                  imageUrl:
                      'https://thumbs.dreamstime.com/b/cartoon-avatar-d-icon-isolated-white-background-perfect-personalization-social-media-visuals-364048000.jpg',
                ),
              ),
            ),
            SizedBox(height: paddingMedium),
            _agentInformation(context),
            _levelRequested(context),
            SizedBox(height: paddingMedium),
            Divider(),
            SizedBox(height: paddingMedium),
            _rejoinReason(context),

            _cadidateIdentity(context),
            _pemantauanPersetujuan(context),
            SizedBox(height: paddingLarge),
          ],
        ),
      ),
    );
  }

  Widget _levelRequested(BuildContext context) {
    if (controller.isNewRequest) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Level Diajukan', style: Theme.of(context).textTheme.bodyMedium),
          SizedBox(height: paddingSmall),
          Theme(
            data: Utils.dropDownThemeData(context),
            child: CustomDropdown(
              items: const ['BP', 'BM', 'BD'],
              initialItem: 'BP',
              onChanged: (val) {},
              closedHeaderPadding: EdgeInsets.all(12),
              decoration: Utils.dropdownDecoration(context),
            ),
          ),
        ],
      );
    }

    return _titleValue(
      context,
      title: 'Level Diajukan',
      value: 'Business Partner',
    );
  }

  Widget _pemantauanPersetujuan(BuildContext context) {
    if (controller.isNewRequest) return SizedBox.shrink();
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Pemantauan Persetujuan',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
            ),
            GestureDetector(
              onTap: () {
                HistoryApprovalRejoinBottomSheet.show();
              },
              child: Text(
                'Lihat Riwayat',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: kColorPaninBlue,
                ),
              ),
            ),
          ],
        ),
        ListView.builder(
          physics: NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            return Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color:
                          Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                    ),
                  ),
                  padding: EdgeInsets.all(12),
                  child: Row(
                    children: [
                      PdlCircleAvatar(
                        source:
                            'https://cdn.cloudflare.steamstatic.com/steamcommunity/public/images/avatars/78/78d6e2f846bb01fad286aaa12e2ab28cea92840e.jpg',
                        width: 44,
                        height: 44,
                        border: true,
                      ),
                      SizedBox(width: paddingSmall),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'BP Budi Santoso Wijaya',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                            StatusLabel(status: Status.diproses),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.symmetric(vertical: paddingSmall),
                  child: Icon(Icons.keyboard_arrow_down),
                ),
              ],
            );
          },
          itemCount: 10,
          shrinkWrap: true,
        ),
      ],
    );
  }

  PdlButton _bottomButton(BuildContext context) {
    if (controller.isNewRequest) {
      return PdlButton(
        controller: controller,
        onPressed: () {
          BottomSheetRejoin.confirmRejoin(context, onTap: () {});
        },
        title: 'Kirim Pengajuan',
      );
    }

    return PdlButton(
      backgroundColor: Colors.transparent,
      borderColor: Colors.red,
      foregorundColor: Colors.red,
      controller: controller,
      onPressed: () => BottomSheetRejoin.cancelRejoin(context, onTap: () {}),
      title: 'Batalkan Pengajuan',
    );
  }

  Column _agentInformation(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _titleHeader(
          context,
          title: 'Informasi Agen',
          subtitle: '(Bergabung Kembali)',
        ),
        SizedBox(height: paddingMedium),
        _titleValue(context, title: 'Kode Agen', value: 'GA 123'),
        _titleValue(context, title: 'Nama Cabang', value: '11114444'),
        _titleValue(context, title: 'Lokasi Cabang', value: 'Jakarta Barat'),
        _titleValue(context, title: 'Nama Lengkap', value: 'Zainab Hurairah'),
        _titleValue(
          context,
          title: 'Level Sebelumnya',
          value: 'Busines Partner',
        ),
      ],
    );
  }

  Column _rejoinReason(BuildContext context) {
    if (controller.isNewRequest) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _titleHeader(
            context,
            title: 'Alasan Bergabung Kembali',
            subtitle: '(Wajib Diisi)',
          ),
          SizedBox(height: paddingMedium),
          PdlTextField(
            hint: 'Alasan Bergabung Kembali',
            maxLength: 200,
            height: 100,
            isTextArea: true,
            keyboardType: TextInputType.multiline,
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _titleHeader(context, title: 'Alasan Bergabung Kembali'),
        SizedBox(height: paddingSmall),
        Text(
          'Adanya teknologi lorem ipsum dolor sit amet',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        SizedBox(height: paddingMedium),
        Divider(),
        SizedBox(height: paddingMedium),
      ],
    );
  }

  Widget _cadidateIdentity(BuildContext context) {
    final theme = Theme.of(context).textTheme;
    if (controller.isNewRequest) {
      return Container(
        margin: EdgeInsets.only(top: paddingMedium),
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _titleHeader(context, title: 'Foto KTP'),
            SizedBox(height: paddingSmall),
            Obx(() {
              if (controller.ktpImage.value != null) {
                // Display the KTP image if available
                return Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color:
                          Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                    ),
                  ),
                  child: Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(radiusMedium - 2),
                        child: GestureDetector(
                          onTap:
                              () => showImagePreview(
                                path: controller.ktpImage.value!.path,
                              ),
                          child: Image.file(
                            width: 60,
                            height: 60,
                            controller.ktpImage.value!,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      SizedBox(width: paddingSmall),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              path.basename(controller.ktpImage.value!.path),
                            ),
                            Text(
                              '${controller.selectedKtpSize.value!.toStringAsFixed(2)}MB',
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: paddingSmall),
                      GestureDetector(
                        onTap: () => controller.deleteSelectedKtp(),
                        child: Utils.cachedSvgWrapper(
                          'icon/ic-linear-trash-bin.svg',
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                // Show the upload/take photo button if no image
                return GestureDetector(
                  onTap: () => controller.pickKtpImage(context),
                  child: Utils.customDottedBorder(
                    child: Container(
                      width: Get.width,
                      color: Colors.transparent,
                      padding: EdgeInsets.all(paddingMedium),
                      child: Column(
                        children: [
                          Utils.cachedSvgWrapper(
                            'icon/ic-linear-image.svg',
                            color: kColorGlobalBlue,
                            width: 40,
                          ),
                          Text(
                            'Unggah atau ambil foto KTP',
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: kColorGlobalBlue,
                              decoration: TextDecoration.underline,
                              decorationThickness: 2,
                              decorationColor: kColorGlobalBlue,
                              decorationStyle: TextDecorationStyle.solid,
                              height: 2,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }
            }),
            SizedBox(height: paddingSmall),
            Text(
              'Maks. 2MB dengan format JPEG/PNG',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                height: 2,
                color:
                    Get.isDarkMode ? kColorTextTersierLight : kColorTextTersier,
              ),
            ),
          ],
        ),
      );
    }

    final dummyUrlKtp =
        'https://lh3.googleusercontent.com/ehiG9Usj1GuEZcMX4pRj3Gs1z-L8TbOY7HHmSfT_1MrvsrgvD6GSQjLV5a0XryW86h2NeAoXNyMQfuHsWN4txULvnCwHCLOyAQnXNwOXuFDC1aJzvaoONqrwLHFRgEZclfAzR7O_azSUCh6bjG9B-6aZPuszfVNpq0iurE3H8ah9bVbBQvUzSepnOpGGKngVXlOqGnACJw';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: kColorBorderLight),
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(3),
                child: GestureDetector(
                  onTap: () => showImagePreview(path: dummyUrlKtp),
                  child: CachedNetworkImage(
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    imageUrl: dummyUrlKtp,
                  ),
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'KTP saya.png',
                      style: theme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: paddingExtraSmall),
                    Text('2MB', style: theme.bodyMedium),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: paddingMedium),
        Divider(),
        SizedBox(height: paddingMedium),
      ],
    );
  }

  void showImagePreview({required String path}) {
    bool isOnline = path.contains('http');
    Get.dialog(
      Stack(
        children: [
          Center(
            child: Padding(
              padding: EdgeInsets.all(25),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child:
                    isOnline
                        ? CachedNetworkImage(imageUrl: path)
                        : Image.file(File(path)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Row _titleHeader(
    BuildContext context, {
    required String title,
    String? subtitle,
  }) {
    return Row(
      children: [
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        if (subtitle != null && subtitle.isNotEmpty) ...[
          SizedBox(width: paddingExtraSmall),
          Expanded(
            child: Text(
              subtitle,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Color(0xFF888888)),
            ),
          ),
        ],
      ],
    );
  }

  Container _titleValue(
    BuildContext context, {
    required String title,
    required String value,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: paddingMedium),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ),
          Expanded(
            flex: 1,
            child: Text(':', style: Theme.of(context).textTheme.bodyMedium),
          ),
          Expanded(
            flex: 4,
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }
}
