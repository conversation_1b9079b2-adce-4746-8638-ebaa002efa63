import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_circle_avatar.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/rejoin/rejoin_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RequestedRejoinListPage extends StatelessWidget {
  RequestedRejoinListPage({super.key});

  final RejoinController controller = Get.put(RejoinController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Bergabung Kembali BP',
      controller: controller,
      onRefresh: () {},
      child: Padding(
        padding: const EdgeInsets.all(paddingMedium),
        child: Column(
          children: [
            TitleWidget(title: 'Pilih BP untuk bergabung kembali'),
            SizedBox(height: paddingMedium),
            SizedBox(
              width: Get.width,
              child: PdlTextField(
                hint: 'Cari nama atau kode agen',
                onChanged: (value) {},
                prefixIcon: Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child: Utils.cachedSvgWrapper(
                    'icon/ic-linear-search -2.svg',
                    color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                  ),
                ),
              ),
            ),
            SizedBox(height: paddingMedium),
            for (int i = 0; i < 10; i++) _UserCard(),
          ],
        ),
      ),
    );
  }
}

class _EmptyData extends StatelessWidget {
  const _EmptyData({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          Utils.cachedSvgWrapper(
            'icon/img-no-content.svg',
            width: 220,
            height: 220,
          ),
          SizedBox(height: paddingLarge),
          Text(
            'Hmmm.. belum ada data tersedia',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            ),
          ),
          SizedBox(height: paddingExtraLarge),
        ],
      ),
    );
  }
}

class _UserCard extends StatelessWidget {
  const _UserCard({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          () => Get.toNamed(Routes.DETAIL_REJOIN, arguments: {'is_new': true}),
      child: Container(
        width: Get.width,
        padding: EdgeInsets.only(
          left: paddingMedium,
          right: paddingMedium,
          top: paddingMedium,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                PdlCircleAvatar(
                  source:
                      'https://cdn.cloudflare.steamstatic.com/steamcommunity/public/images/avatars/78/78d6e2f846bb01fad286aaa12e2ab28cea92840e.jpg',
                  border: true,
                  height: 44,
                  width: 44,
                ),

                SizedBox(width: paddingMedium),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'BP Budi Santoso Wijaya',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                            SizedBox(height: paddingExtraSmall),
                            Text(
                              '000 1234',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingSmall),
            Divider(),
          ],
        ),
      ),
    );
  }
}
