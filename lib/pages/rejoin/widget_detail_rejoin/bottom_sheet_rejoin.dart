import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_dialog_content.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class BottomSheetRejoin {
  static confirmRejoin(BuildContext context, {required VoidCallback onTap}) {
    PdlBaseDialog(
      context: context,
      child: PdlDialogContent(
        textButtonPos: 'Ya',
        message: 'Yakin ingin mengajukan bergabung kembali BP?',
        onTap: () {
          Get.back();
          confirmChangePolis(context, onTap: onTap);
        },
      ),
    );
  }

  static confirmChangePolis(
    BuildContext context, {
    required VoidCallback onTap,
  }) {
    PdlBaseDialog(
      context: context,
      child: PdlDialogContent(
        textButtonPos: 'Ya, Setuju',
        icon: Utils.cachedSvgWrapper('icon/ic-dialog-exclamation.svg'),
        message:
            '<PERSON>gan permohonan perpindahan pemeliharaan polis ini, maka saya dan agen yang ditunjuk bersedia menerima seluruh hak dan kewajiban sebagaimana mestinya yang tercantum dalam perjanjian keagenan Asuransi Jiwa.',
        onTap: () {
          Get.back();
        },
      ),
    );
  }

  static cancelRejoin(BuildContext context, {required VoidCallback onTap}) {
    PdlBaseDialog(
      child: Column(
        children: [
          Utils.cachedSvgWrapper(
            'icon/ic-dialog-question.svg',
            width: 60,
            height: 60,
          ),
          SizedBox(height: paddingMedium),
          Text(
            'Batalkan Pengajuan ?',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: paddingLarge),
          PdlTextField(
            hint: 'Masukkan Alasan Pembatalan',
            label: 'Alasan Pembatalan',
            maxLength: 120,
            height: 100,
            isTextArea: true,
            keyboardType: TextInputType.multiline,
          ),
          SizedBox(height: paddingLarge),
          SizedBox(
            width: Get.width,
            child: Row(
              children: [
                Expanded(
                  child: FilledButton(
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all(
                        Colors.transparent,
                      ),
                      side: WidgetStateProperty.all(
                        BorderSide(color: Color(0xFFD1D1D1)),
                      ),
                      foregroundColor: WidgetStateProperty.all(
                        Color(0xFF0C9DEB),
                      ),
                      padding: WidgetStateProperty.all(
                        EdgeInsets.symmetric(horizontal: paddingSmall),
                      ),
                    ),
                    child: Text(
                      'label_cancel'.tr,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    onPressed: () => Get.back(),
                  ),
                ),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: FilledButton(
                    onPressed: () {
                      Get.back();
                      onTap();
                    },
                    child: Text('Ya'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      context: context,
    );
  }
}
