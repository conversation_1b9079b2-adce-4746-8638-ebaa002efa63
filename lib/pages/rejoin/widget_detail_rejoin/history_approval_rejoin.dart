import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/components/status_label.dart';
import 'package:pdl_superapp/components/vertical_dash_line.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class HistoryApprovalRejoinBottomSheet {
  static show() {
    PdlBottomSheet(
      content: Container(
        margin: EdgeInsets.only(top: paddingExtraLarge),
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: 3,

          itemBuilder: (context, index) {
            return _ItemHistoryApproval(isLast: (index + 1) == 3);
          },
        ),
      ),
      title: 'Riwayat Persetujuan',
    );
  }
}

class _ItemHistoryApproval extends StatelessWidget {
  const _ItemHistoryApproval({super.key, required this.isLast});
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator and line
          SizedBox(
            width: 40,
            child: Column(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: kColorPaninBlue,
                    shape: BoxShape.circle,
                    border: Border.all(color: kColorGlobalBlue100, width: 3),
                  ),
                ),
                Expanded(child: VerticalDashLine()),
                if (isLast)
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: kColoTuatara400,
                      shape: BoxShape.circle,
                      border: Border.all(color: kColorBorderLight, width: 3),
                    ),
                  ),
              ],
            ),
          ),
          // Content card
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Card(
                color: Colors.transparent,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: Colors.grey.shade200),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('17/08/2025 16:00', style: textTheme.bodyMedium),
                          StatusLabel(status: Status.disetujui),
                        ],
                      ),
                      const SizedBox(height: paddingSmall),
                      Text(
                        'BD Budi santoso Wijaya',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: paddingSmall),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Catatan : ', style: textTheme.bodyMedium),
                          const SizedBox(width: paddingExtraSmall),
                          Expanded(
                            child: Text('-', style: textTheme.bodyMedium),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _EmptyHistory extends StatelessWidget {
  const _EmptyHistory({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          Utils.cachedSvgWrapper(
            'icon/illustration-empty-no-found.svg',
            width: 150,
            height: 150,
          ),
          SizedBox(height: paddingLarge),
          Text(
            'Belum ada riwayat persetujuan',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            ),
          ),
          SizedBox(height: paddingExtraLarge),
        ],
      ),
    );
  }
}
