import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/utils.dart';

class SliverWrapper extends StatelessWidget {
  final List<Widget> widget;
  final Widget expandContent;
  final Widget collapseContent;
  const SliverWrapper({
    super.key,
    required this.widget,
    required this.expandContent,
    required this.collapseContent,
  });

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverAppBar(
          expandedHeight: 200.0, // Height when expanded
          floating: false,
          pinned: true, // Stays pinned when scrolled
          toolbarHeight: 75,
          flexibleSpace: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
              // Check the scroll offset to determine text style
              double percent =
                  (constraints.maxHeight - kToolbarHeight) /
                  (200 - kToolbarHeight);
              bool isExpanded = percent > 0.8;

              return Stack(
                fit: StackFit.expand,
                children: [
                  // Background Image
                  SizedBox(
                    width: Get.width,
                    child: Utils.cachedImageWrapper(
                      'image/img-bg-mobile.png',
                      fit: BoxFit.cover,
                      alignment: Alignment.topCenter,
                    ),
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      width: Get.width,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                      ),
                    ),
                  ),
                  // Title with different styles
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child:
                          isExpanded
                              ? expandContent
                              : Padding(
                                padding: EdgeInsets.only(bottom: 30),
                                child: collapseContent,
                              ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        SliverList(delegate: SliverChildListDelegate(widget)),
      ],
    );
  }
}
