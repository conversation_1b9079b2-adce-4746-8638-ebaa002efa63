import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/table_card.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/widget/kompensasi_page_controller.dart';
import 'package:pdl_superapp/models/kompensasi_item_model.dart';
import 'package:pdl_superapp/utils/constants.dart';

class KompensasiPage extends StatelessWidget {
  KompensasiPage({super.key});

  final KompensasiPageController controller = Get.put(
    KompensasiPageController(),
    tag: "kompensasi-page-${DateTime.timestamp()}",
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      title: 'Estimasi Kompensasi',
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(() {
          if (controller.isLoading.value ||
              controller.kompensasiController.isLoading.isTrue) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(paddingLarge),
                child: CircularProgressIndicator(),
              ),
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: paddingMedium,
            children: [
              const SizedBox(height: paddingSmall),
              TitleWidget(title: 'Periode'),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radiusSmall),
                  color: Colors.grey.shade200,
                ),
                padding: const EdgeInsets.all(paddingSmall),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      controller.kompensasiController.periode.value,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(width: paddingSmall),
                    Text(controller.kompensasiController.periodeTanggal.value),
                  ],
                ),
              ),
              // TableCard with yearly commission data
              Obx(
                () => TableCard(
                  headers: ["Komisi Dasar"],
                  rows: controller.yearlyData,
                  // Header has custom column widths (2:1 ratio)
                  headerColumnWidths: {
                    0: const FlexColumnWidth(
                      2,
                    ), // First header column takes 2 parts
                    1: const FlexColumnWidth(
                      0,
                    ), // Second header column takes 1 part
                  },
                  // Make the last row (total) bold
                  boldRows:
                      controller.yearlyData.isEmpty
                          ? []
                          : [controller.yearlyData.length - 1],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(radiusSmall),
                ),
                padding: const EdgeInsets.all(paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Estimasi Komisi Berjalan',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: paddingSmall),
                    // Dynamic Table
                    Obx(() {
                      if (controller.kompensasiController.isLoading.isTrue) {
                        return Center(child: CircularProgressIndicator());
                      }

                      return Table(
                        columnWidths: const {
                          0: FlexColumnWidth(2),
                          1: FlexColumnWidth(1),
                        },
                        children: [
                          // Generate rows for each item
                          ...controller.kompensasiController.items.map(
                            (item) => _buildTableRow(item),
                          ),
                          // Total row
                          _buildTableRow(
                            KompensasiItemModel(
                              label: 'Total',
                              value:
                                  controller
                                      .kompensasiController
                                      .totalValue
                                      .value,
                              isTotal: true,
                            ),
                          ),
                        ],
                      );
                    }),
                  ],
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  TableRow _buildTableRow(KompensasiItemModel item) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: paddingSmall),
          child: Text(
            item.label,
            style: TextStyle(
              fontWeight: item.isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: paddingSmall),
          child: Text(
            item.value,
            textAlign: TextAlign.end,
            style: TextStyle(
              fontWeight: item.isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }
}
