import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/flavors.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PageWrapper extends StatelessWidget {
  final Widget child;
  PageWrapper({super.key, required this.child});
  final RxString appVersion = ''.obs;

  getAppVersion() async {
    appVersion.value = await Utils.getFullVersionApp();
  }

  @override
  Widget build(BuildContext context) {
    getAppVersion();
    return Stack(
      children: [
        child,
        if (F.appFlavor != Flavor.production)
          Positioned(right: 0, top: 120, child: _flavorBanner()),
      ],
    );
  }

  IgnorePointer _flavorBanner() {
    return IgnorePointer(
      child: Material(
        color: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.symmetric(
            vertical: paddingExtraSmall,
            horizontal: paddingSmall,
          ),
          color: kColorError.withValues(alpha: 0.4),
          child: Obx(
            () => Text(
              '${F.name} $appVersion',
              textDirection: TextDirection.ltr,
              style: Theme.of(Get.context!).textTheme.bodySmall,
            ),
          ),
        ),
      ),
    );
  }
}
