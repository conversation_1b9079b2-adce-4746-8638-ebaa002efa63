import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/filter_button.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/widget/spaj_controller.dart';
import 'package:pdl_superapp/models/widget/widget_spaj_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class SpajPage extends StatelessWidget {
  SpajPage({super.key});

  final SpajController controller = Get.put(SpajController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      title: 'Status SPAJ',
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(() {
          if (controller.isLoading.isTrue) {
            return Center(child: CircularProgressIndicator());
          }

          if (controller.hasError.isTrue) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 8),
                  Text('Error: ${controller.errorMessage.value}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => controller.load(),
                    child: const Text('Coba Lagi'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              const SizedBox(height: paddingMedium),
              // Search and Filter
              SizedBox(
                width: Get.width,
                child: Row(
                  children: [
                    Expanded(
                      child: PdlTextField(
                        hint: 'Cari nama nasabah atau nomor SPAJ...',
                        textController: controller.searchController,
                        onChanged: (value) => controller.onSearch(value),
                        prefixIcon: Padding(
                          padding: EdgeInsets.all(paddingMedium),
                          child: Utils.cachedSvgWrapper(
                            'icon/ic-linear-search -2.svg',
                            color: Get.isDarkMode ? kColorTextDark : null,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: paddingMedium),
                    FilterButton(
                      notificationCount: controller.filterValue.length,
                      content: _filterContent(context),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: paddingMedium),

              // Show tabs for BM role
              if (controller.userLevel != kLevelBP) _buildTabSelector(),

              // Content based on role and selected tab
              _buildContent(context),
            ],
          );
        }),
      ),
    );
  }

  // Tab selector for BM role
  Widget _buildTabSelector() {
    return Container(
      margin: const EdgeInsets.only(bottom: paddingMedium),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(200),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToIndividu(),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        controller.selectedSection.value == 0
                            ? Colors.white
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(200),
                    border: Border.all(
                      color:
                          controller.selectedSection.value == 0
                              ? Colors.grey
                              : Colors.transparent,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Individu',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                      color:
                          controller.selectedSection.value == 0
                              ? Colors.black
                              : Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToTeam(),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        controller.selectedSection.value == 1
                            ? Colors.white
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(200),
                    border: Border.all(
                      color:
                          controller.selectedSection.value == 1
                              ? Colors.grey
                              : Colors.transparent,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Team',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                      color:
                          controller.selectedSection.value == 1
                              ? Colors.black
                              : Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent(BuildContext context) {
    if (controller.arrData.isEmpty) {
      return Column(
        children: [
          const Center(
            child: Padding(
              padding: EdgeInsets.all(paddingMedium),
              child: Text("Tidak ada data SPAJ"),
            ),
          ),
          WidgetLoadMore(onTap: () => controller.load()),
        ],
      );
    }

    return Column(
      children: [
        for (int i = 0; i < controller.arrData.length; i++)
          _card(
            context,
            spajData: controller.arrData[i],
            isLast: i == controller.arrData.length - 1,
          ),
        const SizedBox(height: paddingMedium),
      ],
    );
  }

  SizedBox _filterContent(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          _filterCard(context, spajStatus: kSpajStatPendAdmin),
          _filterCard(context, spajStatus: kSpajStatPendUw),
          _filterCard(context, spajStatus: kSpajStatPendCheck),
          _filterCard(context, spajStatus: kSpajStatAccept),
          _filterCard(context, spajStatus: kSpajStatReject),
          _filterCard(context, spajStatus: kSpajStatPolicy),
          SizedBox(height: paddingMedium),
          Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.1),
                  blurRadius: 10, // Softness of the shadow
                  spreadRadius: 0, // How much the shadow spreads
                  offset: Offset(0, 0), // Shadow position
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.only(bottom: paddingMedium),
              child: Row(
                children: [
                  Expanded(
                    child: PdlButton(
                      title: "Hapus Filter",
                      borderColor: Colors.transparent,
                      backgroundColor: Colors.transparent,
                      foregorundColor:
                          Get.isDarkMode ? kColorTextDark : kColorTextLight,
                      onPressed: () => controller.onTapClearFilter(),
                    ),
                  ),
                  SizedBox(width: paddingMedium),
                  Expanded(
                    child: PdlButton(
                      title: "Terapkan",
                      onPressed: () => controller.onTapTerapkan(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _filterCard(BuildContext context, {required String spajStatus}) {
    return GestureDetector(
      onTap: () => controller.onTapFilter(spajStatus),
      child: Container(
        padding: EdgeInsets.only(
          bottom: paddingSmall,
          left: paddingMedium,
          right: paddingMedium,
        ),
        color: Colors.transparent,
        child: Row(
          children: [
            Obx(() {
              if (controller.filterValue.contains(spajStatus)) {
                return Icon(Icons.check_box, color: kColorGlobalBlue);
              } else {
                return Icon(
                  Icons.check_box_outline_blank,
                  color: Color(0xFFD1D1D1),
                );
              }
            }),
            SizedBox(width: 4),
            Expanded(
              child: Text(
                spajStatus,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _card(context, {required WidgetSpajModels spajData, bool? isLast}) {
    return Container(
      padding: EdgeInsets.only(top: paddingSmall),
      width: Get.width,
      child: Column(
        children: [
          if (controller.userLevel != kLevelBP)
            Padding(
              padding: EdgeInsets.only(bottom: paddingSmall),
              child: Row(
                children: [
                  CircleAvatar(
                    child: Center(child: Text(spajData.agentName?[0] ?? '-')),
                  ),
                  SizedBox(width: paddingSmall),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          spajData.agentName ?? '-',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w700),
                        ),
                        Text(
                          spajData.agentCode ?? '-',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          _cardTitle(context, spajData: spajData),
          SizedBox(height: paddingSmall),

          _cardContent(context, spajData: spajData),
          if (isLast != true) Divider(),
        ],
      ),
    );
  }

  Padding _cardContent(context, {required WidgetSpajModels spajData}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingSmall),
      child: Column(
        children: [
          _contentField(
            context,
            title: 'Pemegang Polis',
            value: spajData.policyHolderName ?? '-',
          ),
          _contentField(
            context,
            title: 'Premi',
            value: Utils.currencyFormatters(
              data: spajData.basicPremium.toString(),
              currency: spajData.currency,
            ),
          ),
          _contentField(
            context,
            title: 'Periode Pembayaran',
            value: spajData.frequency ?? '-',
          ),
          if (spajData.spajStatus != kSpajStatAccept)
            _contentField(
              context,
              title: 'Tanggal Submit',
              value: DateFormat(
                'MM/dd/yy',
              ).format(DateTime.parse(spajData.submitDate!)),
            ),
        ],
      ),
    );
  }

  Row _contentField(context, {required String title, required String value}) {
    return Row(
      children: [
        Expanded(child: Text(title)),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
      ],
    );
  }

  Row _cardTitle(context, {required WidgetSpajModels spajData}) {
    return Row(
      children: [
        Expanded(
          child: Text(
            spajData.spajNumber ?? '-',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color:
                spajData.spajStatus == kSpajStatAccept
                    ? kColorGlobalBgGreen
                    : kColorGlobalBgWarning,
            borderRadius: BorderRadius.circular(8),
          ),
          padding: EdgeInsets.all(paddingExtraSmall),
          child: Text(
            spajData.spajStatus ?? '-',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color:
                  spajData.spajStatus == kSpajStatAccept
                      ? kColorGlobalGreen
                      : kColorGlobalWarning,
            ),
          ),
        ),
      ],
    );
  }
}
