import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/routes/app_routes.dart';

class DeepLinkParser {
  DeepLinkParser._();
  static final _instance = DeepLinkParser._();
  factory DeepLinkParser() => _instance;

  final _appLinks = AppLinks();

  Future<void> initAppLink() async {
    Future.delayed(Duration(seconds: 2)).then((val) async {
      if (await _appLinks.getInitialLink() != null) {
        Uri uriLink = await _appLinks.getInitialLink() ?? Uri();
        parseData(uriLink);
      }
      _appLinks.uriLinkStream.listen((uri) {
        parseData(uri);
      });
    });
    // if (fragment.contains('/product/')) {
    //   var lastIndexOfSlash = fragment.lastIndexOf('/');
    //   if (lastIndexOfSlash == fragment.length - 1) {
    //     return const ProductList();
    //   }
    //   String id = fragment.substring(lastIndexOfSlash + 1);
    //   return ProductScreen.withId(id: id);
    // }

    // return const Home();
  }

  parseData(Uri uri) {
    String path = uri.path;
    // !!IMPORTANT: this is only if we can't accept encoded URL in the first place, bcs when using cmd to perform this on emulator we couldnt use un-encoded url
    // decode -> re encode
    // decode uri from encoded to normal
    String decode = Uri.decodeFull(uri.toString());
    // parse again so we can get params
    Uri uriParse = Uri.parse(decode);
    if (path.contains('/forget-password')) {
      String token = uriParse.queryParameters['token'].toString();
      Get.toNamed(Routes.RESET_PASSWORD, arguments: {'token': token});
    }
  }
}
