import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';

class NetworkManager extends GetxController {
  final _isOnline = true.obs;
  final _isConnectionPoor = false.obs;
  final _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _subscription;

  static const int _timeoutSeconds = 5;
  static const String _testHost = 'google.com'; // atau API endpoint Anda
  static const int _poorConnectionThreshold =
      2000; // 2 detik dalam milliseconds

  bool get isOnline => _isOnline.value && !_isConnectionPoor.value;

  @override
  void onInit() {
    super.onInit();
    checkConnectivity();
    _subscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
    );
  }

  Future<bool> checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
      return _isOnline.value && !_isConnectionPoor.value;
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error checking connectivity: $e');
      } catch (_) {
        log('Error checking connectivity: $e'); // Fallback to direct log
      }
      return false;
    }
  }

  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    final wasOnline = _isOnline.value;
    _isOnline.value = result != ConnectivityResult.none;

    if (_isOnline.value) {
      // Cek kualitas koneksi hanya jika terdeteksi online
      await _checkConnectionQuality();
    } else {
      _isConnectionPoor.value =
          false; // Reset poor connection state when offline
    }

    // Tampilkan notifikasi hanya jika status berubah
    if (_isOnline.value != wasOnline || _isConnectionPoor.value) {
      _showConnectionStatus();
    }
  }

  void _showConnectionStatus() {
    if (!_isOnline.value) {
      Get.find<LoggerService>().log('Network connection lost');
    } else if (_isConnectionPoor.value) {
      Get.find<LoggerService>().log(
        'Poor network connection, switching to offline mode',
      );
    } else {
      Get.find<LoggerService>().log('Network connection restored');
    }
  }

  Future<void> _checkConnectionQuality() async {
    try {
      final stopwatch = Stopwatch()..start();

      final result = await InternetAddress.lookup(
        _testHost,
      ).timeout(Duration(seconds: _timeoutSeconds));

      if (result.isNotEmpty) {
        final pingTime = stopwatch.elapsedMilliseconds;
        _isConnectionPoor.value = pingTime > _poorConnectionThreshold;
        Get.find<LoggerService>().log(
          'Connection quality check: ${pingTime}ms',
        );
      }
    } catch (e) {
      Get.find<LoggerService>().log('Connection quality check failed: $e');
      _isConnectionPoor.value = true;
    }
  }

  // Method publik untuk mengecek kualitas koneksi secara manual
  Future<bool> checkConnectionQuality() async {
    if (!_isOnline.value) return false;
    await _checkConnectionQuality();
    return !_isConnectionPoor.value;
  }

  @override
  void onClose() {
    _subscription.cancel();
    super.onClose();
  }
}
