import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pdl_superapp/utils/constants.dart';

/// The [AppTheme] defines light and dark themes for the app.
///
/// Theme setup for FlexColorScheme package v8.
/// Use same major flex_color_scheme package version. If you use a
/// lower minor version, some properties may not be supported.
/// In that case, remove them after copying this theme to your
/// app or upgrade package to version 8.1.1.
///
/// Use in [MaterialApp] like this:
///
/// MaterialApp(
///   theme: AppTheme.light,
///   darkTheme: AppTheme.dark,
/// );
abstract final class ThemePrimary {
  // The defined light theme.
  static ThemeData light = FlexThemeData.light(
    colors: const FlexSchemeColor(
      // Custom colors
      primary: Color(0xFF0C9DEB),
      primaryContainer: Color(0xFFD0E4FF),
      primaryLightRef: Color(0xFF0C9DEB),
      secondary: Color(0xFFF6F6F6),
      secondaryContainer: Color(0xFFFFDBCF),
      secondaryLightRef: Color(0xFFF6F6F6),
      tertiary: Color(0xFFE7E7E7),
      tertiaryContainer: Color(0xFF95F0FF),
      tertiaryLightRef: Color(0xFFE7E7E7),
      appBarColor: Color(0xFFFFDBCF),
      error: Color(0xFFBA1A1A),
      errorContainer: Color(0xFFFFDAD6),
    ),
    subThemesData: const FlexSubThemesData(
      interactionEffects: true,
      tintedDisabledControls: true,
      useMaterial3Typography: true,
      useM2StyleDividerInM3: true,
      filledButtonRadius: 8.0,
      inputDecoratorIsFilled: true,
      inputDecoratorBorderType: FlexInputBorderType.outline,
      alignedDropdown: true,
      navigationRailUseIndicator: true,
      navigationRailLabelType: NavigationRailLabelType.all,
    ),
    surface: Color(0xFFFFFFFF),
    surfaceTint: Color(0xFFFFFFFF),
    textTheme: modifyTextTheme(GoogleFonts.interTextTheme()),
    primaryTextTheme: modifyTextTheme(GoogleFonts.interTextTheme()),
    scaffoldBackground: kColorBgLight,
    useMaterial3ErrorColors: true,
    visualDensity: FlexColorScheme.comfortablePlatformDensity,
    cupertinoOverrideTheme: const CupertinoThemeData(applyThemeToAll: true),
  );
  // The defined dark theme.

  static ThemeData dark = FlexThemeData.dark(
    colors: const FlexSchemeColor(
      // Custom colors
      primary: Color(0xFF0C9DEB),
      primaryContainer: Color(0xFF0075BD),
      primaryLightRef: Color(0xFF0C9DEB),
      secondary: Color(0xFF34405E),
      secondaryContainer: Color(0xFF872100),
      secondaryLightRef: Color(0xFFF6F6F6),
      tertiary: Color(0xFF4F4F4F),
      tertiaryContainer: Color(0xFF283149),
      tertiaryLightRef: Color(0xFFE7E7E7),
      appBarColor: Color(0xFFFFDBCF),
      error: Color(0xFFFFB4AB),
      errorContainer: Color(0xFF93000A),
    ),
    subThemesData: const FlexSubThemesData(
      interactionEffects: true,
      tintedDisabledControls: true,
      blendOnColors: true,
      useMaterial3Typography: true,
      useM2StyleDividerInM3: true,
      filledButtonRadius: 8.0,
      inputDecoratorIsFilled: true,
      inputDecoratorBorderType: FlexInputBorderType.outline,
      alignedDropdown: true,
      navigationRailUseIndicator: true,
      navigationRailLabelType: NavigationRailLabelType.all,
    ),

    surface: Color(0xFF283149),
    surfaceTint: Color(0xFF34405E),
    textTheme: modifyTextTheme(GoogleFonts.interTextTheme(), isDark: true),
    primaryTextTheme: modifyTextTheme(
      GoogleFonts.interTextTheme(),
      isDark: true,
    ),
    scaffoldBackground: kColorBgDark,
    appBarBackground: kColorBgDark,
    useMaterial3ErrorColors: true,
    visualDensity: FlexColorScheme.comfortablePlatformDensity,
    cupertinoOverrideTheme: const CupertinoThemeData(applyThemeToAll: true),
  );
}

TextTheme modifyTextTheme(TextTheme base, {bool? isDark}) {
  Color textColor = isDark == true ? kColorTextTersier : kColorTextLight;
  return base.copyWith(
    displayLarge: TextStyle(color: textColor),
    displayMedium: TextStyle(color: textColor),
    displaySmall: TextStyle(color: textColor),
    headlineLarge: TextStyle(color: textColor),
    headlineMedium: TextStyle(color: textColor),
    headlineSmall: TextStyle(color: textColor),
    titleLarge: TextStyle(color: textColor),
    titleMedium: TextStyle(color: textColor),
    titleSmall: TextStyle(color: textColor),
    bodyLarge: TextStyle(color: textColor),
    bodyMedium: TextStyle(color: textColor),
    bodySmall: TextStyle(color: textColor),
    labelLarge: TextStyle(color: textColor),
    labelMedium: TextStyle(color: textColor),
    labelSmall: TextStyle(color: textColor),
  );
}
