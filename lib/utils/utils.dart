import 'dart:developer' as dev;
import 'dart:io';
import 'dart:math';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_svg_image/cached_network_svg_image.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/languages/languages.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/theme_utils.dart';
import 'package:pdl_superapp/utils/themes/theme_primary.dart';
import 'package:restart_app/restart_app.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

import '../routes/app_routes.dart';

enum ControllerState {
  firstLoad,
  loading,
  loadingSuccess,
  loadingFailed,
  reload,
}

class Utils {
  static Widget cachedImageWrapper(
    url, {
    double? width,
    Color? color,
    BoxFit? fit,
    bool? isFullUrl,
    Alignment? alignment,
  }) {
    // First try to get the asset URL from the theme service
    String themeKey = 'default';
    try {
      // Check if the theme is loaded and if the asset exists in the theme
      if (ThemeUtils.isThemeLoaded) {
        // Get the theme key
        themeKey = ThemeUtils.currentTheme.key;

        // Print the theme key
        _log('Theme key for image: $themeKey');
      }
    } catch (e) {
      _logError('Error getting theme asset URL: $e');
    }

    String baseUrl =
        'https://pdl-superapp-uat-s3.s3.ap-southeast-3.amazonaws.com/themes';
    String activeTheme = themeKey.isNotEmpty ? themeKey : 'default';

    return CachedNetworkImage(
      imageUrl: isFullUrl == true ? url : '$baseUrl/$activeTheme/$url',
      width: width,
      // ignore: deprecated_member_use
      color: color,
      fit: fit ?? BoxFit.contain,
      alignment: alignment ?? Alignment.center,
      errorWidget:
          (context, url, error) => Container(
            color: Colors.grey[300],
            child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
          ),
    );
  }

  static Widget cachedSvgWrapper(
    url, {
    double? width,
    double? height,
    Color? color,
    BoxFit? fit,
  }) {
    // First try to get the asset URL from the theme service

    String themeKey = 'default';

    try {
      // Check if the theme is loaded and if the asset exists in the theme
      if (ThemeUtils.isThemeLoaded) {
        // Get the theme key
        themeKey = ThemeUtils.currentTheme.key;

        // Print the theme key
        _log('Theme key for SVG: $themeKey');
      }
    } catch (e) {
      _logError('Error getting theme asset URL for SVG: $e');
    }

    // Fallback to the default URL if theme asset not found
    String baseUrl =
        'https://pdl-superapp-uat-s3.s3.ap-southeast-3.amazonaws.com/themes';
    String activeTheme = themeKey.isNotEmpty ? themeKey : 'default';

    return CachedNetworkSVGImage(
      '$baseUrl/$activeTheme/$url',
      errorWidget: SvgPicture.asset('assets/$url', fit: fit ?? BoxFit.contain),
      width: width,
      height: height,
      // placeholder: SvgPicture.asset('assets/$url', fit: fit ?? BoxFit.contain),
      // ignore: deprecated_member_use
      color: color,
      fit: fit ?? BoxFit.contain,
    );
  }

  static setLoggedIn({
    required String token,
    required String refreshToken,
    required String username,
    required String password,
  }) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(kStorageToken, token);
    await prefs.setString(kStorageRefreshToken, refreshToken);

    // save encrypted username and password to local devices for Fingerprint
    await prefs.setString(kStorageUsername, username);
    await prefs.setString(kStoragePassword, password);

    Get.offAllNamed(Routes.MAIN);
  }

  static setLoggedOut({bool? noRedirect}) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.remove(kStorageToken);
    prefs.remove(kStorageRefreshToken);
    prefs.remove(kStorageAgentCode);
    prefs.remove(kStorageAgentName);
    prefs.remove(kStorageUserId);
    prefs.remove(kStorageUserFirestoreId);
    if (noRedirect == true) {
      return;
    }
    Get.offAllNamed(Routes.LOGIN);
  }

  static setBiometric() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isActive = prefs.getBool(kStorageIsBiometricActive) ?? false;

    await prefs.setBool(kStorageIsBiometricActive, !isActive);
  }

  static Future<String> getFullVersionApp() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String result = '(${packageInfo.buildNumber})';
    ShorebirdCodePush shorebirdCodePush = ShorebirdCodePush();
    // appVersion.value = packageInfo.version;
    // buildNumber.value = packageInfo.buildNumber;
    int patchNumber = await shorebirdCodePush.currentPatchNumber() ?? 0;
    SnackBar(content: Text('Patch Number $patchNumber'));
    if (patchNumber != 0) {
      result =
          'V-${packageInfo.version}+${packageInfo.buildNumber}#$patchNumber';
    } else {
      result = 'V-${packageInfo.version}+${packageInfo.buildNumber}';
    }
    return result;
  }

  static Future<void> checkForUpdates() async {
    final shorebirdCodePush = ShorebirdCodePush();
    final isUpdateAvailable =
        await shorebirdCodePush.isNewPatchAvailableForDownload();

    if (isUpdateAvailable) {
      // Download the new patch if it's available.
      await shorebirdCodePush.downloadUpdateIfAvailable().then((value) {
        Get.dialog(
          Material(
            color: Colors.transparent,
            child: SizedBox(
              width: Get.width,
              height: Get.height,
              child: Center(
                child: Container(
                  color: kColorBgLight,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'Terdapat pembaharuan aplikasi. \nMemulai ulang aplikasi.',
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: paddingSmall),
                      CircularProgressIndicator(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );

        Future.delayed(
          const Duration(seconds: 2),
        ).then((val) => Restart.restartApp());
      });
    } else {
      SnackBar(content: Text('failed bro'));
    }
  }

  static Future<String> getBuildNumber() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String result = '(${packageInfo.buildNumber})';
    return result;
  }

  static Future<String> getAppVersionOnly() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String result = '(${packageInfo.version})';
    return result;
  }

  static int getMonthDifference(DateTime date) {
    DateTime today = DateTime.now();
    int yearDiff = today.year - date.year;
    int monthDiff = today.month - date.month;

    return (yearDiff * 12) + monthDiff;
  }

  static Future<String> getDeviceId() async {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    String deviceId = '';
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await deviceInfoPlugin.webBrowserInfo;
      deviceId = '${webBrowserInfo.appCodeName}';
    }
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;
      deviceId = androidDeviceInfo.id;
    }
    if (Platform.isIOS) {
      IosDeviceInfo iosDeviceInfo = await deviceInfoPlugin.iosInfo;
      deviceId = '${iosDeviceInfo.identifierForVendor}';
    }
    return deviceId;
  }

  static String getInitials(String name) {
    List<String> words = name.trim().split(' ');
    String initials =
        words
            .map((word) => word.isNotEmpty ? word[0].toUpperCase() : '')
            .join();
    return initials.length > 2 ? initials.substring(0, 2) : initials;
  }

  static imagePicker(
    context, {
    bool? isCamera,
    required Function(XFile) onSuccess,
  }) async {
    final ImagePicker picker = ImagePicker();

    final XFile? image = await picker.pickImage(
      source: isCamera == true ? ImageSource.camera : ImageSource.gallery,
    );

    if (image != null) {
      onSuccess(image);
    }
  }

  static getFileSize(String filepath, int decimals) {
    var file = File(filepath);
    int bytes = file.lengthSync();
    if (bytes <= 0) return "0 B";
    const suffixes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    var i = (log(bytes) / log(1024)).floor();
    return '${(bytes / pow(1024, i)).toStringAsFixed(decimals)} ${suffixes[i]}';
  }

  static bool setTheme(bool isDarkTheme) {
    bool result = false;

    if (isDarkTheme) {
      Get.changeTheme(ThemePrimary.dark);
      result = true;
    } else {
      Get.changeTheme(ThemePrimary.light);
      result = false;
    }

    return result;
  }

  static setLocale(String locale) {
    if (locale == 'id_ID') {
      Languages().changeLocale('Indonesia');
    } else {
      try {
        Languages().changeLocale('English');
      } catch (e) {
        // print('here $e');
      }
    }
  }

  static String currencyFormatters({String? data, String? currency}) {
    final currencyFormatter = NumberFormat('#,##0', 'ID');

    if (data == 'null') {
      data = null;
    }
    String stringNumber = currencyFormatter.format(double.parse(data ?? '0.0'));

    return '${currency ?? 'Rp'}$stringNumber';
  }

  static String getRandomString({int length = 8}) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final rand = Random();
    return List.generate(
      length,
      (_) => chars[rand.nextInt(chars.length)],
    ).join('');
  }

  /// Log message using LoggerService if available
  static void _log(String message) {
    try {
      Get.find<LoggerService>().log('[Utils] $message');
    } catch (_) {
      // Fallback to dev.log if LoggerService is not available
      dev.log('[Utils] $message');
    }
  }

  /// Log error using LoggerService if available
  static void _logError(String message) {
    try {
      Get.find<LoggerService>().log('[Utils] ERROR: $message');
    } catch (_) {
      // Fallback to dev.log if LoggerService is not available
      dev.log('[Utils] ERROR: $message');
    }
  }

  static ThemeData dropDownThemeData(context) {
    final dropdownTheme = Theme.of(context).copyWith(
      inputDecorationTheme: const InputDecorationTheme(
        errorBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        focusedErrorBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
      ),
    );
    return dropdownTheme;
  }

  static CustomDropdownDecoration dropdownDecoration(context) {
    final dropdownDecoration = CustomDropdownDecoration(
      closedBorderRadius: BorderRadius.circular(8),
      expandedBorderRadius: BorderRadius.circular(8),
      closedFillColor: Theme.of(context).colorScheme.surface,
      expandedFillColor: Theme.of(context).colorScheme.surface,
      closedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      expandedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      listItemStyle: Theme.of(context).textTheme.bodyMedium,
      hintStyle: Theme.of(context).textTheme.bodyMedium,
      headerStyle: Theme.of(context).textTheme.bodyMedium,
    );
    return dropdownDecoration;
  }

  static Future<CroppedFile?> cropKtpImage(
    BuildContext context, {
    required String imagePath,
    String title = 'Sesuaikan Ukuran KTP',
  }) async {
    final croppedImage = await ImageCropper().cropImage(
      sourcePath: imagePath,
      aspectRatio: CropAspectRatio(ratioX: ktpAspectRatio, ratioY: 1),
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: title,
          toolbarColor: kColorGlobalBlue,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: true,
          hideBottomControls: false,
        ),
        IOSUiSettings(
          title: title,
          aspectRatioLockEnabled: true,
          resetAspectRatioEnabled: false,
          aspectRatioPickerButtonHidden: true,
        ),
      ],
    );
    return croppedImage;
  }

  static DottedBorder customDottedBorder({required Widget child}) {
    return DottedBorder(
      radius: Radius.circular(8),
      borderType: BorderType.RRect,
      color: Get.isDarkMode ? kColorBorderDark : kColorTextDark,
      dashPattern: [8, 8],
      child: child,
    );
  }

  /// Menampilkan popup notifikasi
  static void popup({required String body, required String type}) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (type) {
      case kPopupSuccess:
        backgroundColor = kColorGlobalBgGreen;
        textColor = kColorGlobalGreen;
        icon = Icons.check_circle;
        break;
      case kPopupFailed:
        backgroundColor = kColorGlobalBgRed;
        textColor = kColorGlobalRed;
        icon = Icons.error;
        break;
      case kPopupWarning:
        backgroundColor = kColorGlobalBgWarning;
        textColor = kColorGlobalWarning;
        icon = Icons.warning;
        break;
      case kPopupInfo:
      default:
        backgroundColor = kColorGlobalBgBlue;
        textColor = kColorGlobalBlue;
        icon = Icons.info;
        break;
    }

    Get.snackbar(
      '',
      '',
      backgroundColor: backgroundColor,
      borderRadius: 8,
      margin: EdgeInsets.all(paddingMedium),
      padding: EdgeInsets.symmetric(
        horizontal: paddingMedium,
        vertical: paddingSmall,
      ),
      titleText: Container(),
      messageText: Row(
        children: [
          Icon(icon, color: textColor),
          SizedBox(width: paddingSmall),
          Expanded(child: Text(body, style: TextStyle(color: textColor))),
        ],
      ),
      duration: Duration(seconds: 3),
    );
  }
}
