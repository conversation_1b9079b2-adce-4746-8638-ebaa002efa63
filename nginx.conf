server {
    listen 8083;
    server_name localhost;

    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # Optional: serve .wasm, .js, etc. with correct MIME types
    location ~* \.(?:ico|css|js|gif|jpe?g|png|woff2?|eot|ttf|svg|wasm)$ {
        try_files $uri =404;
        expires 6M;
        access_log off;
        add_header Cache-Control "public";
    }

    error_page 404 /index.html;
}